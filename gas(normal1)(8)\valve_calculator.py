import tkinter as tk
from tkinter import ttk, messagebox
import math
class ValveCalculator:
    def __init__(self, parent):
        self.parent = parent  # 保存GasCalculator实例的引用
     
        # 从父类引用所需变量
        self.furnace_data = parent.furnace_data if hasattr(parent, 'furnace_data') else []
        
        # 确保引用bypass_vars变量
        if hasattr(parent, 'bypass_vars'):
            self.bypass_vars = parent.bypass_vars
        else:
            self.bypass_vars = {
                'branch_pre': {'pressure': tk.StringVar()},
                'branch_post': {'pressure': tk.StringVar()}
            }
        
        # 引用其他需要的变量
        self.old_flow = parent.old_flow
        self.normal_flow = parent.normal_flow
        self.temperature = parent.temperature
        self.main_valve_pre = parent.main_valve_pre
        self.main_valve_post = parent.main_valve_post
        self.inlet_pressure = parent.inlet_pressure
        self.branch_valve_pre = parent.branch_valve_pre if hasattr(parent, 'branch_valve_pre') else tk.StringVar()
        self.branch_valve_post = parent.branch_valve_post if hasattr(parent, 'branch_valve_post') else tk.StringVar()
        
        # 如果存在这些变量，也引用它们
        self.bypass_branch_pre = parent.bypass_branch_pre if hasattr(parent, 'bypass_branch_pre') else tk.StringVar()
        self.bypass_branch_post = parent.bypass_branch_post if hasattr(parent, 'bypass_branch_post') else tk.StringVar()
        
        # 创建调节阀计算所需的变量
        self.small_valve_vars = []  # 存储小炉阀门的变量
        
         # 从GasCalculator访问相关变量
        # 主阀门变量
        self.main_valve_c_selected_value = parent.main_valve_c_selected_value
        self.main_valve_k_max_value = parent.main_valve_k_max_value
        self.main_valve_k_min_value = parent.main_valve_k_min_value
        
        # 安全阀变量
        self.release_valve_dn_value = parent.release_valve_dn_value
        self.release_valve_area_value = parent.release_valve_area_value
        self.release_valve_d0_value = parent.release_valve_d0_value
        self.release_valve_working_pressure_value = parent.release_valve_working_pressure_value
        
        
        # 保存小炉阀门的数据
        self.small_valve_persistent_data = parent.small_valve_persistent_data
        print(f"阀门计算器初始化时small_valve_persistent_data: {self.small_valve_persistent_data}")

        # 添加标准C值选项列表
        self.standard_c_values = ["0.28", "0.52", "0.93", "1.6", "2.5", "4", "8", "13", "20", 
                                 "25", "45", "72", "100", "175", "280", "365", "640"]
        # 在__init__方法结束时添加
        print(f"初始化时small_valve_persistent_data: {self.small_valve_persistent_data}")


    def show_valve_calculator(self):
        """显示阀门计算窗口"""
        # 在show_valve_calculator方法的开始位置
        print(f"显示阀门计算窗口时small_valve_persistent_data: {self.small_valve_persistent_data}")
        # 如果窗口已经打开，则聚焦到该窗口
        if 'valve_window' in self.parent.open_windows and self.parent.open_windows['valve_window'].winfo_exists():
            self.parent.open_windows['valve_window'].focus_set()
            return
        
        # 先保存当前项目状态
        self.parent.save_project_silent()
        # 调试日志
        print(f"打开窗口前self.small_valve_persistent_data: {self.small_valve_persistent_data}")
        # 直接从parent重新获取一次最新数据
        self.small_valve_persistent_data = self.parent.small_valve_persistent_data
        
        
        # 保存当前窗口打开前的所有状态
        original_state = {
            "is_oxygen_lance": self.parent.has_oxygen_lance.get(),
            "is_oxygen_kiln": self.parent.is_oxygen_kiln.get(),
            "main_valve_c_selected": self.main_valve_c_selected_value.get(),
            "main_valve_k_max": self.main_valve_k_max_value.get(),
            "main_valve_k_min": self.main_valve_k_min_value.get(),
            "release_valve_dn": self.release_valve_dn_value.get(),
            "release_valve_working_pressure": self.release_valve_working_pressure_value.get(),
            "release_valve_d0": self.release_valve_d0_value.get(),
            "release_valve_area": self.release_valve_area_value.get() if hasattr(self, 'release_valve_area_value') else ""
        }
        
        # 存储原始状态为窗口的属性，以便关闭时使用
        self._valve_window_original_state = original_state
        
        # 先计算窗口位置，使其居中显示
        main_width = self.parent.root.winfo_width()
        main_height = self.parent.root.winfo_height()
        main_x = self.parent.root.winfo_rootx()
        main_y = self.parent.root.winfo_rooty()
        
        win_width = 1050  # 根据实际窗口大小调整
        win_height = 650  # 根据实际窗口大小调整
        
        # 计算居中位置
        x = main_x + (main_width - win_width) // 2
        y = main_y + (main_height - win_height) // 2
        
        # 创建顶层窗口，并在创建时就指定位置
        valve_window = tk.Toplevel(self.parent.root)
        valve_window.title("调节阀计算")
        
        # 先隐藏窗口，配置完成后再显示
        valve_window.withdraw()
        
        # 设置窗口大小和位置
        valve_window.geometry(f"{win_width}x{win_height}+{x}+{y}")
        
        # 【新增】将窗口添加到管理系统
        self.parent.open_windows['valve_window'] = valve_window
        
        # 防止用户调整窗口大小
        valve_window.resizable(False, False)
        
        # 计算窗口位置，使其居中
        self.parent.center_window(valve_window)
        
        # 创建主管调节阀部分（原主阀门部分）
        main_valve_frame = ttk.LabelFrame(valve_window, text="主管调节阀")
        main_valve_frame.pack(fill=tk.X, padx=5, pady=5)

        # 使用更清晰的网格布局
        # 第一行
        ttk.Label(main_valve_frame, text="C计大:").grid(row=0, column=0, padx=5, pady=5, sticky="e")
        self.main_valve_c_max = tk.StringVar()
        ttk.Label(main_valve_frame, textvariable=self.main_valve_c_max, width=8).grid(row=0, column=1, padx=5, sticky="w")

        ttk.Label(main_valve_frame, text="C计小:").grid(row=0, column=2, padx=5, pady=5, sticky="e")
        self.main_valve_c_min = tk.StringVar()
        ttk.Label(main_valve_frame, textvariable=self.main_valve_c_min, width=8).grid(row=0, column=3, padx=5, sticky="w")

        ttk.Label(main_valve_frame, text="总管阀前管径选取:").grid(row=0, column=4, padx=5, pady=5, sticky="e")
        ttk.Label(main_valve_frame, textvariable=self.parent.main_pre_selected, width=8).grid(row=0, column=5, padx=5, sticky="w")

        # 第二行
        ttk.Label(main_valve_frame, text="C选定:").grid(row=1, column=0, padx=5, pady=5, sticky="e")
        self.main_valve_c_selected = tk.StringVar(value=self.main_valve_c_selected_value.get())
        main_valve_c_selected_combo = ttk.Combobox(main_valve_frame, textvariable=self.main_valve_c_selected, width=8)
        main_valve_c_selected_combo.configure(values=self.standard_c_values)
        main_valve_c_selected_combo.grid(row=1, column=1, padx=5, sticky="w")

        # 添加对C选定变化的监听，触发保存和更新设备表
        self.main_valve_c_selected.trace_add("write", self.save_main_valve_c_selected)

        ttk.Label(main_valve_frame, text="K大:").grid(row=1, column=2, padx=5, pady=5, sticky="e")
        self.main_valve_k_max = tk.StringVar(value=self.main_valve_k_max_value.get())
        ttk.Label(main_valve_frame, textvariable=self.main_valve_k_max, width=8).grid(row=1, column=3, padx=5, sticky="w")

        ttk.Label(main_valve_frame, text="阀门选取管径(mm):").grid(row=1, column=4, padx=5, pady=5, sticky="e")
        self.main_valve_diameter = tk.StringVar(value=self.parent.main_valve_diameter_value.get() or "")
        diameter_values = ["15", "20", "25", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400", "450", "500", "550", "600"]
        main_valve_diameter_combo = ttk.Combobox(main_valve_frame, textvariable=self.main_valve_diameter, width=8, values=diameter_values)
        main_valve_diameter_combo.grid(row=1, column=5, padx=5, sticky="w")
        # 阀门选取管径控件创建后添加
        main_valve_diameter_combo.grid(row=1, column=5, padx=5, sticky="w")
        # 绑定阀门选取管径变化事件，保存值
        self.main_valve_diameter.trace_add("write", self.save_main_valve_diameter)

        # 第三行
        ttk.Label(main_valve_frame, text="K小:").grid(row=2, column=0, padx=5, pady=5, sticky="e")
        self.main_valve_k_min = tk.StringVar(value=self.main_valve_k_min_value.get())
        ttk.Label(main_valve_frame, textvariable=self.main_valve_k_min, width=8).grid(row=2, column=1, padx=5, sticky="w")

        # 将阀门阀芯尺寸放在阀门选取管径的正下方
        ttk.Label(main_valve_frame, text="阀门阀芯尺寸(mm):").grid(row=2, column=4, padx=5, pady=5, sticky="e")
        self.main_valve_core_size = tk.StringVar(value=self.parent.main_valve_core_size_value.get() if hasattr(self.parent, 'main_valve_core_size_value') else "")
        core_size_values = ["15", "20", "25", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300"]
        main_valve_core_size_combo = ttk.Combobox(main_valve_frame, textvariable=self.main_valve_core_size, width=8, values=core_size_values)
        main_valve_core_size_combo.grid(row=2, column=5, padx=5, sticky="w")
        # 阀门阀芯尺寸控件创建后添加
        main_valve_core_size_combo.grid(row=2, column=5, padx=5, sticky="w")
        # 绑定阀门阀芯尺寸变化事件，保存值
        self.main_valve_core_size.trace_add("write", self.save_main_valve_core_size)
        
        # 【高亮修改】先绑定事件，再执行计算
        # 绑定相关变量的变化事件，自动重新计算C值
        self.old_flow.trace_add("write", self.calculate_main_valve_c_values)
        self.normal_flow.trace_add("write", self.calculate_main_valve_c_values)
        self.temperature.trace_add("write", self.calculate_main_valve_c_values)
        self.main_valve_pre.trace_add("write", self.calculate_main_valve_c_values)
        self.main_valve_post.trace_add("write", self.calculate_main_valve_c_values)
        # 【高亮修改】执行初始计算 - 确保在UI元素创建后执行
        print("开始执行初始C值计算")
        self.calculate_main_valve_c_values()
        
        # 绑定阀门选取管径变化事件，保存值
        self.main_valve_diameter.trace_add("write", self.save_main_valve_diameter)

        # 绑定阀门阀芯尺寸变化事件，保存值
        self.main_valve_core_size.trace_add("write", self.save_main_valve_core_size)
        
        # 小炉阀门部分
        small_valve_frame = ttk.LabelFrame(valve_window, text="小炉阀门")
        small_valve_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 创建滚动区域
        canvas = tk.Canvas(small_valve_frame)
        scrollbar = ttk.Scrollbar(small_valve_frame, orient=tk.VERTICAL, command=canvas.yview)
        self.small_valve_scrollable_frame = ttk.Frame(canvas)
        
        canvas.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        canvas.create_window((0, 0), window=self.small_valve_scrollable_frame, anchor="nw")
        
        # 更新小炉阀门显示
        self.update_small_valve_display()
        
        # 【修改此处】放散阀门部分
        release_valve_frame = ttk.LabelFrame(valve_window, text="放散阀门")
        release_valve_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 【修改此处】放散阀门输入字段
        row = 0
        # 进车间压力字段 - 与主界面联动
        ttk.Label(release_valve_frame, text="进车间压力(MPa):").grid(row=row, column=0, padx=5, pady=2)
        self.release_valve_pressure = tk.StringVar()
        ttk.Entry(release_valve_frame, textvariable=self.release_valve_pressure).grid(row=row, column=1)
        # 绑定进车间压力变化事件，自动同步到主界面并重新计算
        self.release_valve_pressure.trace_add("write", self.sync_pressure_to_main)
        # 初始化进车间压力值
        self.release_valve_pressure.set(self.inlet_pressure.get() or "")
        # 绑定主界面压力变化事件，自动同步到放散阀门
        self.inlet_pressure.trace_add("write", self.sync_pressure_to_release_valve)
        
        row += 1
        # 工作压力字段
        ttk.Label(release_valve_frame, text="工作压力(MPa):").grid(row=row, column=0, padx=5, pady=2)
        self.release_valve_working_pressure = tk.StringVar()
        ttk.Entry(release_valve_frame, textvariable=self.release_valve_working_pressure).grid(row=row, column=1)
        # 默认值
        self.release_valve_working_pressure.set("")
        # 绑定工作压力变化事件，自动计算放散阀截面积
        self.release_valve_working_pressure.trace_add("write", self.calculate_release_valve_area)
        # 【新增】绑定工作压力变化事件，保存值
        self.release_valve_working_pressure.trace_add("write", self.save_release_valve_working_pressure)
        # 【新增】设置保存的工作压力值，自动触发计算
        self.release_valve_working_pressure.set(self.release_valve_working_pressure_value.get())

        
        row += 1
        # 放散阀截面积A字段 - 计算值
        ttk.Label(release_valve_frame, text="放散阀截面积A:").grid(row=row, column=0, padx=5, pady=2)
        self.release_valve_area = tk.StringVar()
        ttk.Label(release_valve_frame, textvariable=self.release_valve_area).grid(row=row, column=1)
        
        row += 1
        # 选取DN字段 - 输入值
        ttk.Label(release_valve_frame, text="选取DN:").grid(row=row, column=0, padx=5, pady=2)
        self.release_valve_dn = tk.StringVar(value=self.release_valve_dn_value.get())  # 使用保存的值
        ttk.Entry(release_valve_frame, textvariable=self.release_valve_dn).grid(row=row, column=1)
        # 绑定选取DN变化事件，自动计算反算d0
        self.release_valve_dn.trace_add("write", self.calculate_release_valve_d0)
        # 【新增】绑定选取DN变化事件，保存值
        self.release_valve_dn.trace_add("write", self.save_release_valve_dn)
        
        row += 1
        # 反算d0字段 - 计算值
        ttk.Label(release_valve_frame, text="反算d0:").grid(row=row, column=0, padx=5, pady=2)
        self.release_valve_d0 = tk.StringVar()
        ttk.Label(release_valve_frame, textvariable=self.release_valve_d0).grid(row=row, column=1)
        
        # 绑定相关变量的变化事件，自动计算放散阀截面积
        self.old_flow.trace_add("write", self.calculate_release_valve_area)
        
        # 初始计算放散阀截面积
        self.calculate_release_valve_area()
        # 所有配置完成后，显示窗口
        valve_window.deiconify()
        # 【新增】在窗口关闭时保存所有值
        # 【在此处修改】在窗口关闭时保存所有值
        def on_closing():
            # 保存主管调节阀值
            try:
                # 保存主管调节阀值和放散阀值
                # 确保变量已经正确初始化
                if not hasattr(self, 'main_valve_c_selected_value'):
                    self.main_valve_c_selected_value = tk.StringVar()
                if not hasattr(self, 'main_valve_k_max_value'):
                    self.main_valve_k_max_value = tk.StringVar()
                if not hasattr(self, 'main_valve_k_min_value'):
                    self.main_valve_k_min_value = tk.StringVar()
                if not hasattr(self, 'release_valve_dn_value'):
                    self.release_valve_dn_value = tk.StringVar()
                if not hasattr(self, 'release_valve_working_pressure_value'):
                    self.release_valve_working_pressure_value = tk.StringVar()
                if not hasattr(self, 'release_valve_d0_value'):
                    self.release_valve_d0_value = tk.StringVar()
                if not hasattr(self, 'release_valve_area_value'):
                    self.release_valve_area_value = tk.StringVar()
                # 保存主管调节阀值
                self.main_valve_c_selected_value.set(self.main_valve_c_selected.get())
                self.main_valve_k_max_value.set(self.main_valve_k_max.get())
                self.main_valve_k_min_value.set(self.main_valve_k_min.get())
                
                # 添加这两行保存阀门选取管径和阀门阀芯尺寸
                if hasattr(self.parent, 'main_valve_diameter_value'):
                    self.parent.main_valve_diameter_value.set(self.main_valve_diameter.get())
                if hasattr(self.parent, 'main_valve_core_size_value'):
                    self.parent.main_valve_core_size_value.set(self.main_valve_core_size.get())
                # 保存放散阀选取DN值
                self.release_valve_dn_value.set(self.release_valve_dn.get())
                # 【新增】保存放散阀工作压力和反算d0
                self.release_valve_working_pressure_value.set(self.release_valve_working_pressure.get())
                self.release_valve_d0_value.set(self.release_valve_d0.get())
                self.release_valve_area_value.set(self.release_valve_area.get())
                
                # 【新增】保存小炉阀门C选定值
                for i, valve_data in enumerate(self.small_valve_data):
                    if i < len(self.small_valve_persistent_data):
                        # 确保small_valve_persistent_data[i]是字典
                        if not isinstance(self.small_valve_persistent_data[i], dict):
                            self.small_valve_persistent_data[i] = {}
                        
                        # 保存C选定值
                        c_value = valve_data['c_selected'].get()
                        self.small_valve_persistent_data[i]['c_selected'] = c_value
                        print(f"关闭窗口时保存小炉{i+1}的C选定值: {c_value}")
                        
                        # 保存阀门阀芯尺寸值
                        core_size = valve_data['valve_core_size'].get()
                        self.small_valve_persistent_data[i]['valve_core_size'] = core_size
                        print(f"关闭窗口时保存小炉{i+1}的阀门阀芯尺寸值: {core_size}")
                    else:
                        # 如果索引超出范围，添加新条目
                        self.small_valve_persistent_data.append({
                            'c_selected': c_value,
                            'valve_core_size': core_size
                        })
                        print(f"添加并保存小炉{i+1}的C选定值: {c_value}")
                        print(f"添加并保存小炉{i+1}的阀门阀芯尺寸值: {core_size}")
                
                # 确保保存到父类的持久化数据中
                self.parent.small_valve_persistent_data = self.small_valve_persistent_data
                
                # 静默保存项目
                self.parent.save_project_silent()

                # 保存阀门选取管径和阀门阀芯尺寸
                if hasattr(self.parent, 'main_valve_diameter_value'):
                    self.parent.main_valve_diameter_value.set(self.main_valve_diameter.get())
                if hasattr(self.parent, 'main_valve_core_size_value'):
                    self.parent.main_valve_core_size_value.set(self.main_valve_core_size.get())
            except Exception as e:
                print(f"保存阀门计算值时出错: {str(e)}")
                traceback.print_exc()  # 打印详细错误栈
            finally:
                valve_window.destroy()
        # 添加窗口关闭事件处理
        def on_valve_window_close():
            try:
                # 保存数据
                self.save_valve_data()
                # 从窗口管理系统中移除
                if 'valve_window' in self.parent.open_windows:
                    del self.parent.open_windows['valve_window']
                # 关闭窗口
                valve_window.destroy()
            except Exception as e:
                print(f"关闭阀门窗口时出错: {str(e)}")
                valve_window.destroy()
                    
            
        valve_window.protocol("WM_DELETE_WINDOW", on_closing)

    def calculate_valve_diameter(self, flow, pressure):
        """计算阀门口径
        Args:
            flow: 流量(Nm³/h)
            pressure: 压力(MPa)
        Returns:
            diameter: 计算口径(mm)
        """
        # 这里使用简化的计算公式，实际应用中需要根据具体要求调整
        diameter = math.sqrt((4 * flow) / (3.14 * 3600 * 20 * math.sqrt(pressure * 1e6)))  # 20m/s为假设流速
        return diameter * 1000  # 转换为mm
    # 新增同步方法
    def sync_main_selected_diameter(self, *args):
        """同步主界面的总管阀前管径选取到调节阀计算窗口"""
        try:
            if hasattr(self.parent, 'main_pre_selected_diameter'):
                selected_diameter = self.parent.main_pre_selected_diameter.get()
                self.parent.main_pre_selected.set(selected_diameter)
                print(f"同步总管阀前管径选取: {selected_diameter}")
        except Exception as e:
            print(f"同步总管阀前管径选取时出错: {str(e)}")
    # 【新增方法】同步进车间压力到主界面
    def sync_pressure_to_main(self, *args):
        """同步放散阀门中的进车间压力到主界面，并重新计算"""
        try:
            # 获取放散阀门中的压力值
            pressure_str = self.release_valve_pressure.get()
            
            # 同步到主界面的inlet_pressure
            if hasattr(self.parent, 'inlet_pressure') and pressure_str:
                self.parent.inlet_pressure.set(pressure_str)
                print(f"同步进车间压力到主界面: {pressure_str}")
                
                # 触发设备表压力更新
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_valve_pressure'):
                    self.parent.equipment_manager.update_valve_pressure()
        except Exception as e:
            print(f"同步压力到主界面时出错: {str(e)}")
    
    # 【新增方法】同步主界面压力到放散阀门
    def sync_pressure_to_release_valve(self, *args):
        """同步主界面的进车间压力到放散阀门"""
        try:
            # 检查是否正在更新中，避免循环调用
            if hasattr(self, '_updating_pressure') and self._updating_pressure:
                return
                
            # 获取主界面的进车间压力
            main_pressure = self.inlet_pressure.get()
            
            # 更新放散阀门的进车间压力
            if main_pressure and main_pressure != self.release_valve_pressure.get():
                self.release_valve_pressure.set(main_pressure)
                print(f"已同步主界面压力到放散阀门: {main_pressure}")
        except Exception as e:
            print(f"同步主界面压力到放散阀门时出错: {str(e)}")
    
    # 【新增方法】计算放散阀截面积
    def calculate_release_valve_area(self, *args):
        """计算放散阀截面积A"""
        try:
            # 获取输入值
            old_flow = float(self.old_flow.get() or 0)  # 窑老期天然气流量
            working_pressure_str = self.release_valve_working_pressure.get()
            if not working_pressure_str:  # 如果为空，则不计算
                self.release_valve_area.set("")
                return
            working_pressure = float(working_pressure_str) # 工作压力，默认0.18MPa
            
            # 检查输入是否有效
            if old_flow <= 0 or working_pressure <= 0:
                self.release_valve_area.set("")
                return
            
            # 计算放散阀截面积A
            # 放散阀截面积A=窑老期流量*0.743/784.5/0.8/1.1/（工作压力）
            area = old_flow * 0.743 / 784.5 / 0.8 / 1.1 / working_pressure
            
            # 更新显示
            self.release_valve_area.set(f"{area:.4f}")
            # 保存截面积到持久化变量
            self.release_valve_area_value.set(f"{area:.4f}")
            print(f"计算放散阀截面积: 窑老期流量={old_flow}, 工作压力={working_pressure}, 截面积={area:.4f}")
            
            # 如果已有选取DN，计算反算d0
            self.calculate_release_valve_d0()
            
        except Exception as e:
            print(f"计算放散阀截面积时出错: {str(e)}")
            self.release_valve_area.set("")
    
     # 【修改方法】计算反算d0
    def calculate_release_valve_d0(self, *args):
        """计算反算d0"""
        try:
            # 获取输入值
            area_str = self.release_valve_area.get()
            
            # 检查输入是否有效
            if not area_str:
                self.release_valve_d0.set("")
                return
            
            try:
                area = float(area_str)
                
                # 检查数值是否有效
                if area <= 0:
                    self.release_valve_d0.set("")
                    return
                
                # 计算反算d0
                # 反算d0=20*sqrt(放散阀截面积A/3.14)
                d0 = 20 * math.sqrt(area / 3.14)
                
                # 更新显示
                self.release_valve_d0.set(f"{d0:.2f}")
                 # 保存反算d0到持久化变量
                self.release_valve_d0_value.set(f"{d0:.2f}")
                print(f"计算反算d0: 截面积={area}, 反算d0={d0:.2f}")
                
            except ValueError:
                self.release_valve_d0.set("")
                
        except Exception as e:
            print(f"计算反算d0时出错: {str(e)}")
            self.release_valve_d0.set("")
        # 【新增方法】保存主管调节阀C选定值
    def save_main_valve_c_selected(self, *args):
        """保存主管调节阀C选定值"""
        try:
            if hasattr(self.parent, 'main_valve_c_selected_value'):
                # 获取当前值
                c_selected = self.main_valve_c_selected.get()
                # 设置到父对象
                self.parent.main_valve_c_selected_value.set(c_selected)
                # 静默保存项目
                self.parent.save_project_silent()
                
                # 【新增代码】调用设备表更新方法，实时更新设备3的CV值
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_valve_c_selected'):
                    self.parent.equipment_manager.update_valve_c_selected()
                
        except Exception as e:
            print(f"保存主管调节阀C选定值时出错: {str(e)}")
    
    # 【新增方法】保存主管调节阀K值
    def save_main_valve_k_values(self, *args):
        """保存主管调节阀K大和K小值"""
        try:
            self.main_valve_k_max_value.set(self.main_valve_k_max.get())
            self.main_valve_k_min_value.set(self.main_valve_k_min.get())
        except Exception as e:
            print(f"保存主管调节阀K值时出错: {str(e)}")
    
    # 【新增方法】保存放散阀选取DN值
    def save_release_valve_dn(self, *args):
        """保存放散阀选取DN值"""
        try:
            self.release_valve_dn_value.set(self.release_valve_dn.get())
        except Exception as e:
            print(f"保存放散阀选取DN值时出错: {str(e)}")
    def save_release_valve_working_pressure(self, *args):
        """保存放散阀工作压力值"""
        try:
            self.release_valve_working_pressure_value.set(self.release_valve_working_pressure.get())
        except Exception as e:
            print(f"保存放散阀工作压力值时出错: {str(e)}")

    def save_release_valve_d0(self, *args):
        """保存放散阀反算d0值"""
        try:
            self.release_valve_d0_value.set(self.release_valve_d0.get())
        except Exception as e:
            print(f"保存放散阀反算d0值时出错: {str(e)}")

    def update_small_valve_display(self):
        """更新小炉阀门显示 - 按平均热负荷和浮动值分组"""
        try:
            # 获取小炉数量
            furnace_count = len(self.parent.furnace_data)
            print(f"小炉数量: {furnace_count}")

            # 确保小炉阀门持久化数据数组大小与小炉数量一致，但保留已有数据
            print(f"调整数组前small_valve_persistent_data: {self.small_valve_persistent_data}")
            while len(self.small_valve_persistent_data) < furnace_count:
                self.small_valve_persistent_data.append({
                    'c_selected': '',
                    'k_max': '',
                    'k_min': '',
                    'valve_diameter': '',  # 阀门选取管径的持久化数据
                    'valve_core_size': ''  # 添加阀门阀芯尺寸的持久化数据
                })
            print(f"调整数组后small_valve_persistent_data: {self.small_valve_persistent_data}")

            # 清除现有显示
            for widget in self.small_valve_scrollable_frame.winfo_children():
                widget.destroy()

            # 创建表头
            headers = ["小炉编号", "平均热负荷(%)", "浮动值(%)", "C计大", "C计小", "选取阀前管径", "阀门选取管径", "阀门阀芯尺寸", "C选定", "喷枪数", "K大", "K小",
                    "单枪最大流量(Nm³/h)", "单枪正常流量(Nm³/h)", "单枪最小流量(Nm³/h)"]
            for col, header in enumerate(headers):
                ttk.Label(self.small_valve_scrollable_frame, text=header).grid(row=0, column=col, padx=5, pady=2)

            # 按平均热负荷和浮动值对小炉进行分组
            furnace_groups = self.group_furnaces_by_heat_load_and_float()
            print(f"小炉分组情况: {furnace_groups}")

            # 创建分组显示
            self.small_valve_data = []
            self.furnace_group_mapping = {}  # 存储分组到小炉索引的映射

            row_index = 1
            for group_key, furnace_indices in furnace_groups.items():
                group_row = self.create_group_row(row_index, group_key, furnace_indices)
                self.small_valve_data.append(group_row)
                self.furnace_group_mapping[row_index-1] = furnace_indices
                row_index += 1
            
            # 如果bypass_vars存在，绑定事件
            if hasattr(self, 'bypass_vars'):
                if 'branch_pre' in self.bypass_vars and 'pressure' in self.bypass_vars['branch_pre']:
                    self.bypass_vars['branch_pre']['pressure'].trace_add("write", self.calculate_all_group_c_values)

                if 'branch_post' in self.bypass_vars and 'pressure' in self.bypass_vars['branch_post']:
                    self.bypass_vars['branch_post']['pressure'].trace_add("write", self.calculate_all_group_c_values)

            # 初始计算所有分组的单枪流量和C值
            for group_index in range(len(self.small_valve_data)):
                print(f"初始计算分组{group_index+1}的数据")
                # 确保计算单枪流量
                self.calculate_group_per_nozzle_flow(group_index)
                # 确保计算C值
                self.calculate_group_c_values(group_index)

        except Exception as e:
            messagebox.showerror("错误", f"更新小炉阀门显示时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    def group_furnaces_by_heat_load_and_float(self):
        """按平均热负荷和浮动值对小炉进行分组"""
        furnace_groups = {}

        if not hasattr(self.parent, 'furnace_data') or not self.parent.furnace_data:
            return furnace_groups

        for i, furnace in enumerate(self.parent.furnace_data):
            heat_load = furnace['heat_load'].get() if 'heat_load' in furnace else ""
            float_value = furnace['float_value'].get() if 'float_value' in furnace else ""

            # 创建组键
            group_key = f"{heat_load}_{float_value}"

            if group_key not in furnace_groups:
                furnace_groups[group_key] = []

            furnace_groups[group_key].append(i)

        return furnace_groups

    def create_group_row(self, row_index, group_key, furnace_indices):
        """创建分组行"""
        group_row = {}

        # 解析组键
        heat_load, float_value = group_key.split('_')

        # 小炉编号 - 显示所有小炉编号
        furnace_numbers = [str(i+1) for i in furnace_indices]
        furnace_text = f"小炉{','.join(furnace_numbers)}"
        ttk.Label(self.small_valve_scrollable_frame, text=furnace_text).grid(row=row_index, column=0, padx=5, pady=2)

        # 平均热负荷
        ttk.Label(self.small_valve_scrollable_frame, text=heat_load).grid(row=row_index, column=1, padx=5, pady=2)

        # 浮动值
        ttk.Label(self.small_valve_scrollable_frame, text=float_value).grid(row=row_index, column=2, padx=5, pady=2)

        # C计大
        c_max_var = tk.StringVar()
        ttk.Label(self.small_valve_scrollable_frame, textvariable=c_max_var).grid(row=row_index, column=3, padx=5, pady=2)
        group_row['c_max'] = c_max_var

        # C计小
        c_min_var = tk.StringVar()
        ttk.Label(self.small_valve_scrollable_frame, textvariable=c_min_var).grid(row=row_index, column=4, padx=5, pady=2)
        group_row['c_min'] = c_min_var

        # 选取阀前管径 - 显示第一个小炉的值
        furnace_selected_diameter_var = tk.StringVar()
        if furnace_indices and hasattr(self.parent, 'furnace_data') and furnace_indices[0] < len(self.parent.furnace_data):
            furnace_selected_diameter = self.parent.furnace_data[furnace_indices[0]]['selected_diameter'].get()
            furnace_selected_diameter_var.set(furnace_selected_diameter)
        ttk.Label(self.small_valve_scrollable_frame, textvariable=furnace_selected_diameter_var).grid(row=row_index, column=5, padx=5, pady=2)
        group_row['furnace_selected_diameter'] = furnace_selected_diameter_var

        # 阀门选取管径 - 可手动选择，使用第一个小炉的保存值
        valve_diameter_var = tk.StringVar()
        if furnace_indices and furnace_indices[0] < len(self.small_valve_persistent_data) and 'valve_diameter' in self.small_valve_persistent_data[furnace_indices[0]]:
            saved_valve_diameter = self.small_valve_persistent_data[furnace_indices[0]]['valve_diameter']
            if saved_valve_diameter:
                valve_diameter_var.set(saved_valve_diameter)

        diameter_values = ["15", "20", "25", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300", "350", "400", "450", "500", "550", "600"]
        valve_diameter_combo = ttk.Combobox(self.small_valve_scrollable_frame, textvariable=valve_diameter_var, width=8, values=diameter_values)
        valve_diameter_combo.grid(row=row_index, column=6, padx=5, pady=2)
        group_row['valve_diameter'] = valve_diameter_var

        # 阀门阀芯尺寸 - 使用第一个小炉的保存值
        valve_core_size_var = tk.StringVar()
        if furnace_indices and furnace_indices[0] < len(self.small_valve_persistent_data) and 'valve_core_size' in self.small_valve_persistent_data[furnace_indices[0]]:
            saved_core_size = self.small_valve_persistent_data[furnace_indices[0]]['valve_core_size']
            if saved_core_size:
                valve_core_size_var.set(saved_core_size)

        core_size_values = ["15", "20", "25", "40", "50", "65", "80", "100", "125", "150", "200", "250", "300"]
        valve_core_size_combo = ttk.Combobox(self.small_valve_scrollable_frame, textvariable=valve_core_size_var, width=8, values=core_size_values)
        valve_core_size_combo.grid(row=row_index, column=7, padx=5, pady=2)
        group_row['valve_core_size'] = valve_core_size_var

        # C选定 - 使用第一个小炉的保存值
        c_selected_var = tk.StringVar()
        if furnace_indices and furnace_indices[0] < len(self.small_valve_persistent_data) and 'c_selected' in self.small_valve_persistent_data[furnace_indices[0]]:
            saved_value = self.small_valve_persistent_data[furnace_indices[0]]['c_selected']
            if saved_value:
                c_selected_var.set(saved_value)

        c_selected_combo = ttk.Combobox(self.small_valve_scrollable_frame, textvariable=c_selected_var, width=8)
        c_selected_combo.configure(values=self.standard_c_values)
        c_selected_combo.grid(row=row_index, column=8, padx=5, pady=2)
        group_row['c_selected'] = c_selected_var

        # 喷枪数 - 显示第一个小炉的值
        nozzle_count_var = tk.StringVar()
        if furnace_indices and hasattr(self.parent, 'furnace_data') and furnace_indices[0] < len(self.parent.furnace_data):
            furnace_nozzle_count = self.parent.furnace_data[furnace_indices[0]]['nozzle_count'].get()
            nozzle_count_var.set(furnace_nozzle_count)
        nozzle_entry = ttk.Entry(self.small_valve_scrollable_frame, textvariable=nozzle_count_var, width=8)
        nozzle_entry.grid(row=row_index, column=9, padx=5, pady=2)
        group_row['nozzle_count'] = nozzle_count_var

        # K大
        k_max_var = tk.StringVar()
        if furnace_indices and furnace_indices[0] < len(self.small_valve_persistent_data) and 'k_max' in self.small_valve_persistent_data[furnace_indices[0]]:
            saved_k_max = self.small_valve_persistent_data[furnace_indices[0]]['k_max']
            if saved_k_max:
                k_max_var.set(saved_k_max)
        ttk.Label(self.small_valve_scrollable_frame, textvariable=k_max_var).grid(row=row_index, column=10, padx=5, pady=2)
        group_row['k_max'] = k_max_var

        # K小
        k_min_var = tk.StringVar()
        if furnace_indices and furnace_indices[0] < len(self.small_valve_persistent_data) and 'k_min' in self.small_valve_persistent_data[furnace_indices[0]]:
            saved_k_min = self.small_valve_persistent_data[furnace_indices[0]]['k_min']
            if saved_k_min:
                k_min_var.set(saved_k_min)
        ttk.Label(self.small_valve_scrollable_frame, textvariable=k_min_var).grid(row=row_index, column=11, padx=5, pady=2)
        group_row['k_min'] = k_min_var

        # 单枪最大流量
        max_flow_per_nozzle_var = tk.StringVar()
        ttk.Label(self.small_valve_scrollable_frame, textvariable=max_flow_per_nozzle_var).grid(row=row_index, column=12, padx=5, pady=2)
        group_row['max_flow_per_nozzle'] = max_flow_per_nozzle_var

        # 单枪正常流量
        normal_flow_per_nozzle_var = tk.StringVar()
        ttk.Label(self.small_valve_scrollable_frame, textvariable=normal_flow_per_nozzle_var).grid(row=row_index, column=13, padx=5, pady=2)
        group_row['normal_flow_per_nozzle'] = normal_flow_per_nozzle_var

        # 单枪最小流量
        min_flow_per_nozzle_var = tk.StringVar()
        ttk.Label(self.small_valve_scrollable_frame, textvariable=min_flow_per_nozzle_var).grid(row=row_index, column=14, padx=5, pady=2)
        group_row['min_flow_per_nozzle'] = min_flow_per_nozzle_var

        # 存储分组信息
        group_row['furnace_indices'] = furnace_indices
        group_row['group_key'] = group_key

        # 绑定事件
        group_index = row_index - 1
        c_selected_var.trace_add("write", lambda *args, idx=group_index: self.calculate_group_k_values(idx))
        c_selected_var.trace_add("write", lambda *args, idx=group_index: self.save_group_c_selected(idx))
        nozzle_count_var.trace_add("write", lambda *args, idx=group_index: self.sync_group_nozzle_count_to_main_and_calculate(idx))
        valve_diameter_var.trace_add("write", lambda *args, idx=group_index: self.save_group_valve_diameter(idx))
        valve_core_size_var.trace_add("write", lambda *args, idx=group_index: self.save_group_valve_core_size(idx))

        # 绑定主界面阀前管径变化事件
        if furnace_indices and hasattr(self.parent, 'furnace_data') and furnace_indices[0] < len(self.parent.furnace_data):
            self.parent.furnace_data[furnace_indices[0]]['selected_diameter'].trace_add("write",
                lambda *args, idx=group_index, var=furnace_selected_diameter_var:
                self.sync_group_furnace_diameter(idx, var))

        return group_row

    def calculate_all_group_c_values(self, *args):
        """计算所有分组的C计大和C计小"""
        try:
            for group_index in range(len(self.small_valve_data)):
                self.calculate_group_c_values(group_index)
        except Exception as e:
            print(f"计算所有分组C值时出错: {str(e)}")

    def calculate_group_c_values(self, group_index):
        """计算分组的C计大和C计小"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])

            if not furnace_indices:
                return

            # 使用第一个小炉的数据进行计算
            first_furnace_index = furnace_indices[0]

            if first_furnace_index >= len(self.parent.furnace_data):
                return

            # 获取小炉流量 - 使用总流量
            max_flow_str = self.parent.furnace_data[first_furnace_index]['max_flow'].cget("text") if 'max_flow' in self.parent.furnace_data[first_furnace_index] else ""
            min_flow_str = self.parent.furnace_data[first_furnace_index]['min_flow'].cget("text") if 'min_flow' in self.parent.furnace_data[first_furnace_index] else ""

            # 获取压力
            pre_pressure_str = self.branch_valve_pre.get()
            post_pressure_str = self.branch_valve_post.get()

            # 获取温度
            temperature_str = self.temperature.get()

            # 检查数据有效性
            if not max_flow_str or not min_flow_str or not pre_pressure_str or not post_pressure_str or not temperature_str:
                return

            try:
                # 转换为浮点数
                max_flow = float(max_flow_str)
                min_flow = float(min_flow_str)
                pre_pressure = float(pre_pressure_str)
                post_pressure = float(post_pressure_str)
                temperature = float(temperature_str)

                # 检查压力差是否有效
                if pre_pressure <= post_pressure:
                    return

                # 计算压力差
                delta_p = (pre_pressure - post_pressure) * 10

                # 将压力单位从MPa转换为bar (1 MPa = 10 bar)
                pre_pressure_bar = pre_pressure * 10 + 1
                post_pressure_bar = post_pressure * 10 + 1

                # 计算C计大
                c_max = 1.167 * max_flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                    (1 - 0.46 * (delta_p) / (pre_pressure_bar)) / \
                    math.sqrt(delta_p * (pre_pressure_bar))

                # 计算C计小
                c_min = 1.167 * min_flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                    (1 - 0.46 * (delta_p) / (pre_pressure_bar)) / \
                    math.sqrt(delta_p * (pre_pressure_bar))

                # 更新显示
                group_row['c_max'].set(f"{c_max:.2f}" if c_max > 0 else "")
                group_row['c_min'].set(f"{c_min:.2f}" if c_min > 0 else "")

                # 如果C选定已有值，自动计算K值
                self.calculate_group_k_values(group_index)

            except ValueError as ve:
                print(f"转换数值出错: {str(ve)}")
                pass

        except Exception as e:
            print(f"计算分组C值时出错: {str(e)}")

    def calculate_group_k_values(self, group_index):
        """计算分组的K大和K小"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]

            # 获取C值
            c_max_str = group_row['c_max'].get()
            c_min_str = group_row['c_min'].get()
            c_selected_str = group_row['c_selected'].get()

            # 检查输入是否有效
            if not c_max_str or not c_min_str or not c_selected_str:
                group_row['k_max'].set("")
                group_row['k_min'].set("")
                return

            try:
                c_max = float(c_max_str)
                c_min = float(c_min_str)
                c_selected = float(c_selected_str)

                # 检查C选定是否有效
                if c_selected <= 0:
                    group_row['k_max'].set("")
                    group_row['k_min'].set("")
                    return

                # 计算K大
                k_max = 1 + 0.68 * math.log10(c_max / c_selected)

                # 计算K小
                k_min = 1 + 0.68 * math.log10(c_min / c_selected)

                # 更新显示
                group_row['k_max'].set(f"{k_max:.2f}" if k_max > 0 else "")
                group_row['k_min'].set(f"{k_min:.2f}" if k_min > 0 else "")

                # 保存K值到所有相关小炉
                self.save_group_k_values(group_index)

            except ValueError:
                pass
        except Exception as e:
            print(f"计算分组K值时出错: {str(e)}")

    def calculate_group_per_nozzle_flow(self, group_index):
        """计算分组的单枪流量"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])

            if not furnace_indices:
                return

            # 使用第一个小炉的数据进行计算
            first_furnace_index = furnace_indices[0]

            if first_furnace_index >= len(self.parent.furnace_data):
                return

            # 获取小炉总流量
            max_flow_str = self.parent.furnace_data[first_furnace_index]['max_flow'].cget("text") if 'max_flow' in self.parent.furnace_data[first_furnace_index] else ""
            normal_flow_str = self.parent.furnace_data[first_furnace_index]['normal_flow'].cget("text") if 'normal_flow' in self.parent.furnace_data[first_furnace_index] else ""
            min_flow_str = self.parent.furnace_data[first_furnace_index]['min_flow'].cget("text") if 'min_flow' in self.parent.furnace_data[first_furnace_index] else ""

            # 获取喷枪数
            nozzle_count_str = group_row['nozzle_count'].get()

            # 检查数据有效性
            if max_flow_str and normal_flow_str and min_flow_str and nozzle_count_str:
                try:
                    max_flow = float(max_flow_str)
                    normal_flow = float(normal_flow_str)
                    min_flow = float(min_flow_str)
                    nozzle_count = float(nozzle_count_str)

                    # 计算单枪流量
                    if nozzle_count > 0:
                        max_flow_per_nozzle = max_flow / nozzle_count
                        normal_flow_per_nozzle = normal_flow / nozzle_count
                        min_flow_per_nozzle = min_flow / nozzle_count

                        # 更新显示
                        group_row['max_flow_per_nozzle'].set(f"{max_flow_per_nozzle:.2f}")
                        group_row['normal_flow_per_nozzle'].set(f"{normal_flow_per_nozzle:.2f}")
                        group_row['min_flow_per_nozzle'].set(f"{min_flow_per_nozzle:.2f}")

                        # 计算C值
                        self.calculate_group_c_values(group_index)
                except ValueError as ve:
                    print(f"转换数值出错: {str(ve)}")
                    pass

        except Exception as e:
            print(f"计算分组单枪流量时出错: {str(e)}")

    def save_group_c_selected(self, group_index):
        """保存分组的C选定值到所有相关小炉"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])
            c_selected = group_row['c_selected'].get()

            # 将C选定值保存到所有相关小炉的持久化数据中
            for furnace_index in furnace_indices:
                if furnace_index < len(self.small_valve_persistent_data):
                    self.small_valve_persistent_data[furnace_index]['c_selected'] = c_selected
                    print(f"保存小炉{furnace_index+1}的C选定值: {c_selected}")

                    # 更新设备表中对应小炉的Cv值
                    if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_small_valve_specification'):
                        self.parent.equipment_manager.update_small_valve_specification(furnace_index)
                        print(f"已触发小炉{furnace_index+1}设备表Cv值更新")

            # 静默保存项目
            self.parent.save_project_silent()

        except Exception as e:
            print(f"保存分组C选定值时出错: {str(e)}")

    def save_group_k_values(self, group_index):
        """保存分组的K值到所有相关小炉"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])
            k_max = group_row['k_max'].get()
            k_min = group_row['k_min'].get()

            # 将K值保存到所有相关小炉的持久化数据中
            for furnace_index in furnace_indices:
                if furnace_index < len(self.small_valve_persistent_data):
                    self.small_valve_persistent_data[furnace_index]['k_max'] = k_max
                    self.small_valve_persistent_data[furnace_index]['k_min'] = k_min
                    print(f"保存小炉{furnace_index+1}的K值: K大={k_max}, K小={k_min}")

            # 静默保存项目
            self.parent.save_project_silent()

        except Exception as e:
            print(f"保存分组K值时出错: {str(e)}")

    def save_group_valve_diameter(self, group_index):
        """保存分组的阀门选取管径到所有相关小炉"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])
            valve_diameter = group_row['valve_diameter'].get()

            # 将阀门选取管径保存到所有相关小炉的持久化数据中
            for furnace_index in furnace_indices:
                if furnace_index < len(self.small_valve_persistent_data):
                    self.small_valve_persistent_data[furnace_index]['valve_diameter'] = valve_diameter
                    print(f"保存小炉{furnace_index+1}的阀门选取管径: {valve_diameter}")

                    # 更新设备表
                    if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_small_valve_specification'):
                        self.parent.equipment_manager.update_small_valve_specification(furnace_index)

            # 静默保存项目
            self.parent.save_project_silent()

        except Exception as e:
            print(f"保存分组阀门选取管径时出错: {str(e)}")

    def save_group_valve_core_size(self, group_index):
        """保存分组的阀门阀芯尺寸到所有相关小炉"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])
            valve_core_size = group_row['valve_core_size'].get()

            # 将阀门阀芯尺寸保存到所有相关小炉的持久化数据中
            for furnace_index in furnace_indices:
                if furnace_index < len(self.small_valve_persistent_data):
                    self.small_valve_persistent_data[furnace_index]['valve_core_size'] = valve_core_size
                    print(f"保存小炉{furnace_index+1}的阀门阀芯尺寸: {valve_core_size}")

                    # 更新设备表
                    if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_small_valve_specification'):
                        self.parent.equipment_manager.update_small_valve_specification(furnace_index)

            # 静默保存项目
            self.parent.save_project_silent()

        except Exception as e:
            print(f"保存分组阀门阀芯尺寸时出错: {str(e)}")

    def sync_group_nozzle_count_to_main_and_calculate(self, group_index):
        """同步分组的喷枪数到主界面并计算单枪流量"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])
            nozzle_count = group_row['nozzle_count'].get()

            # 将喷枪数同步到所有相关小炉
            for furnace_index in furnace_indices:
                if furnace_index < len(self.parent.furnace_data):
                    self.parent.furnace_data[furnace_index]['nozzle_count'].set(nozzle_count)

            # 重新计算小炉值
            self.parent.calculate_furnace_values()

            # 计算分组的单枪流量
            self.calculate_group_per_nozzle_flow(group_index)

        except Exception as e:
            print(f"同步分组喷枪数时出错: {str(e)}")

    def sync_group_furnace_diameter(self, group_index, var):
        """同步主界面的选取阀前管径到分组显示"""
        try:
            if group_index >= len(self.small_valve_data):
                return

            group_row = self.small_valve_data[group_index]
            furnace_indices = group_row.get('furnace_indices', [])

            if furnace_indices and hasattr(self.parent, 'furnace_data') and furnace_indices[0] < len(self.parent.furnace_data):
                selected_diameter = self.parent.furnace_data[furnace_indices[0]]['selected_diameter'].get()
                var.set(selected_diameter)
        except Exception as e:
            print(f"同步分组阀前管径时出错: {str(e)}")

    def sync_furnace_diameter(self, index, var):
        """同步主界面的选取阀前管径到调节阀计算窗口"""
        try:
            if hasattr(self.parent, 'furnace_data') and index < len(self.parent.furnace_data):
                selected_diameter = self.parent.furnace_data[index]['selected_diameter'].get()
                var.set(selected_diameter)
        except Exception as e:
            print(f"同步阀前管径时出错: {str(e)}")

    def save_valve_diameter(self, index):
        """保存阀门选取管径值"""
        try:
            if index < len(self.small_valve_data) and index < len(self.small_valve_persistent_data):
                valve_diameter = self.small_valve_data[index]['valve_diameter'].get()
                self.small_valve_persistent_data[index]['valve_diameter'] = valve_diameter
                
                # 同样保存到父对象的变量中
                key = f"小炉{index+1}阀门选取管径"
                if not hasattr(self.parent, 'valve_diameters'):
                    self.parent.valve_diameters = {}
                
                self.parent.valve_diameters[key] = valve_diameter
                
                # 立即调用设备表更新方法 - 修改为调用update_small_valve_specification
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_small_valve_specification'):
                    self.parent.equipment_manager.update_small_valve_specification(index)
                    print(f"小炉{index+1}管径变更为{valve_diameter}，已触发设备表更新")
                
                # 静默保存项目
                self.parent.save_project_silent()
                print(f"保存小炉{index+1}阀门选取管径: {valve_diameter}")
        except Exception as e:
            print(f"保存阀门选取管径时出错: {str(e)}")
            
    def save_valve_core_size(self, index):
        """保存阀门阀芯尺寸值"""
        try:
            if index < len(self.small_valve_data) and index < len(self.small_valve_persistent_data):
                valve_core_size = self.small_valve_data[index]['valve_core_size'].get()
                self.small_valve_persistent_data[index]['valve_core_size'] = valve_core_size
                
                # 同样保存到父对象的变量中
                key = f"小炉{index+1}阀门阀芯尺寸"
                if not hasattr(self.parent, 'valve_core_sizes'):
                    self.parent.valve_core_sizes = {}
                
                self.parent.valve_core_sizes[key] = valve_core_size
                
                # 立即调用设备表更新方法 - 修改为调用update_small_valve_specification
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_small_valve_specification'):
                    self.parent.equipment_manager.update_small_valve_specification(index)
                    print(f"小炉{index+1}阀芯尺寸变更为{valve_core_size}，已触发设备表更新")
                
                # 静默保存项目
                self.parent.save_project_silent()
                print(f"保存小炉{index+1}阀门阀芯尺寸: {valve_core_size}")
        except Exception as e:
            print(f"保存阀门阀芯尺寸时出错: {str(e)}")
     
     # 【新增方法】计算所有小炉阀门C值
    def calculate_all_small_valve_c_values(self, *args):
        """计算所有小炉阀门的C计大和C计小"""
        try:
            for i in range(len(self.small_valve_data)):
                self.calculate_small_valve_c_values(i)
        except Exception as e:
            print(f"计算所有小炉阀门C值时出错: {str(e)}")
    # 新增方法：同步喷枪数到主界面
    def sync_nozzle_count_to_main(self, index):
        """同步小炉阀门区域的喷枪数到主界面"""
        try:
            if index < len(self.small_valve_data) and index < len(self.furnace_data):
                # 获取小炉阀门区域的喷枪数
                nozzle_count = self.small_valve_data[index]['nozzle_count'].get()
                
                # 更新主界面的喷枪数，避免循环调用
                if nozzle_count and nozzle_count != self.furnace_data[index]['nozzle_count'].get():
                    # 暂时解除主界面喷枪数的跟踪
                     # 暂时禁用事件触发
                    self._updating_nozzle_count = True
                    
                    # 更新主界面喷枪数
                    self.furnace_data[index]['nozzle_count'].set(nozzle_count)
                    
                    # 重新绑定主界面喷枪数的跟踪
                    trace_id = self.furnace_data[index]['nozzle_count'].trace_add("write", 
                        lambda *args: self.calculate_furnace_values())
                    self.furnace_data[index]['nozzle_count_trace_id'] = trace_id
                    
                    # 重新计算小炉值
                    self.calculate_furnace_values()
                    # 【新增】重置标志
                    self._updating_nozzle_count = False
        except Exception as e:
            # 出错时不显示错误消息，保持静默
            pass 
    # 【新增方法】同步喷枪数到主界面并计算单枪流量
    def sync_nozzle_count_to_main_and_calculate(self, index):
        """同步小炉阀门区域的喷枪数到主界面并计算单枪流量"""
        try:
            # 先同步喷枪数到主界面
            self.sync_nozzle_count_to_main(index)
            
            # 然后计算单枪流量
            self.calculate_per_nozzle_flow(index)
            
        except Exception as e:
            print(f"同步喷枪数并计算单枪流量时出错: {str(e)}") 
    # 【新增方法】计算单枪流量
    def calculate_per_nozzle_flow(self, index):
        """计算单枪流量"""
        try:
            if index < len(self.small_valve_data) and index < len(self.parent.furnace_data):
                print(f"计算小炉{index+1}的单枪流量")
                
                # 获取小炉总流量
                max_flow_str = self.parent.furnace_data[index]['max_flow'].cget("text") if 'max_flow' in self.parent.furnace_data[index] else ""
                normal_flow_str = self.parent.furnace_data[index]['normal_flow'].cget("text") if 'normal_flow' in self.parent.furnace_data[index] else ""
                min_flow_str = self.parent.furnace_data[index]['min_flow'].cget("text") if 'min_flow' in self.parent.furnace_data[index] else ""
                
                print(f"小炉{index+1}流量: 最大={max_flow_str}, 正常={normal_flow_str}, 最小={min_flow_str}")
                
                # 获取喷枪数
                nozzle_count_str = self.small_valve_data[index]['nozzle_count'].get()
                print(f"小炉{index+1}喷枪数: {nozzle_count_str}")
                
                # 检查数据有效性
                if max_flow_str and normal_flow_str and min_flow_str and nozzle_count_str:
                    try:
                        max_flow = float(max_flow_str)
                        normal_flow = float(normal_flow_str)
                        min_flow = float(min_flow_str)
                        nozzle_count = float(nozzle_count_str)
                        
                        # 计算单枪流量
                        if nozzle_count > 0:
                            max_flow_per_nozzle = max_flow / nozzle_count
                            normal_flow_per_nozzle = normal_flow / nozzle_count
                            min_flow_per_nozzle = min_flow / nozzle_count
                            
                            print(f"单枪流量计算结果: 最大={max_flow_per_nozzle:.2f}, 正常={normal_flow_per_nozzle:.2f}, 最小={min_flow_per_nozzle:.2f}")
                            
                            # 更新显示
                            self.small_valve_data[index]['max_flow_per_nozzle'].set(f"{max_flow_per_nozzle:.2f}")
                            self.small_valve_data[index]['normal_flow_per_nozzle'].set(f"{normal_flow_per_nozzle:.2f}")
                            self.small_valve_data[index]['min_flow_per_nozzle'].set(f"{min_flow_per_nozzle:.2f}")
                            
                            # 计算C值
                            self.calculate_small_valve_c_values(index)
                    except ValueError as ve:
                        print(f"转换数值出错: {str(ve)}")
                        pass
                else:
                    print(f"数据不完整，无法计算单枪流量")
        
        except Exception as e:
            print(f"计算单枪流量时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    # 【新增方法】计算总管调节阀C值
    def calculate_main_valve_c_values(self, *args):
        """计算总管调节阀的C计大和C计小"""
        try:
            # 获取输入值
            old_flow = float(self.old_flow.get() or 0)  # 窑老期天然气流量
            normal_flow = float(self.normal_flow.get() or 0)  # 正常生产时天然气流量
            temperature = float(self.temperature.get() or 0)  # 环境温度
            pre_pressure = float(self.main_valve_pre.get() or 0)  # 总管调节阀前压力
            post_pressure = float(self.main_valve_post.get() or 0)  # 总管调节阀后压力
            # 【高亮修改】打印输入值进行调试
            print(f"计算C值输入: old_flow={old_flow}, normal_flow={normal_flow}, temperature={temperature}")
            print(f"计算C值压力: pre_pressure={pre_pressure}, post_pressure={post_pressure}")
            
            # 【高亮修改】检查压力差是否有效
            if pre_pressure <= 0 or post_pressure <= 0 or pre_pressure <= post_pressure:
                print("压力值无效，无法计算C值")
                # 清空结果
                self.main_valve_c_max.set("")
                self.main_valve_c_min.set("")
                return
            # 【高亮修改】检查流量是否有效
            if old_flow <= 0 or normal_flow <= 0:
                print("流量值无效，无法计算C值")
                # 清空结果
                self.main_valve_c_max.set("")
                self.main_valve_c_min.set("")
                return
            # 计算压力差
            delta_p = (pre_pressure - post_pressure)*10
            
            # 【高亮修改】计算C计大 - 使用正确的公式和参数
            # 将压力单位从MPa转换为bar (1 MPa = 10 bar)
            pre_pressure_bar = pre_pressure*10+1
            post_pressure_bar = post_pressure*10+1
            
            
            # 计算C计大
            c_max = 1.167 * old_flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                   (1 - 0.46 * (delta_p) / (pre_pressure_bar)) / \
                   math.sqrt(delta_p * (pre_pressure_bar))
            
            # 计算C计小
            c_min = 1.167 * normal_flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                   (1 - 0.46 * (delta_p) / (pre_pressure_bar)) / \
                   math.sqrt(delta_p * (pre_pressure_bar))
             
             # 【高亮修改】打印计算过程和结果
            print(f"压力(bar): 前压力={pre_pressure_bar}, 后压力={post_pressure_bar}, 压差={delta_p}")
            print(f"计算因子1: {1.167 * math.sqrt(0.743 * (273 + temperature)) / 514}")
            print(f"计算因子2: {1 - 0.46 * (delta_p) / (pre_pressure_bar)}")
            print(f"计算因子3: {math.sqrt(delta_p * pre_pressure_bar)}")
            print(f"计算结果: C计大={c_max:.4f}, C计小={c_min:.4f}")
            # 更新显示
            self.main_valve_c_max.set(f"{c_max:.2f}" if c_max > 0 else "")
            self.main_valve_c_min.set(f"{c_min:.2f}" if c_min > 0 else "")
            
            # 如果C选定已有值，自动计算K值
            self.calculate_main_valve_k_values()
            
        except Exception as e:
            print(f"计算总管调节阀C值时出错: {str(e)}")
            # 出错时不显示错误消息，保持静默
            pass
    # 【新增方法】计算总管调节阀K值
    def calculate_main_valve_k_values(self, *args):
        """计算总管调节阀的K大和K小"""
        try:
            # 获取C值
            c_max_str = self.main_valve_c_max.get()
            c_min_str = self.main_valve_c_min.get()
            c_selected_str = self.main_valve_c_selected.get()
            
            
            # 检查输入是否有效
            if not c_max_str or not c_min_str or not c_selected_str:
                # 清空结果
                self.main_valve_k_max.set("")
                self.main_valve_k_min.set("")
                return
            
            c_max = float(c_max_str)
            c_min = float(c_min_str)
            c_selected = float(c_selected_str)
            
            # 检查C选定是否有效
            if c_selected <= 0:
                # 清空结果
                self.main_valve_k_max.set("")
                self.main_valve_k_min.set("")
                return
            
            # 计算K大
            # K大=1+0.68*log10（C计大/C选定）
            k_max = 1 + 0.68 * math.log10(c_max / c_selected)
            
            # 计算K小
            # K小=1+0.68*log10（C计小/C选定）
            k_min = 1 + 0.68 * math.log10(c_min / c_selected)
            
            # 更新显示
            self.main_valve_k_max.set(f"{k_max:.2f}" if k_max > 0 else "")
            self.main_valve_k_min.set(f"{k_min:.2f}" if k_min > 0 else "")
            
            # 【新增】保存K值
            self.save_main_valve_k_values()
            
        except Exception as e:
            print(f"计算总管调节阀K值时出错: {str(e)}")
            # 出错时不显示错误消息，保持静默
            pass
    # 【新增方法】计算小炉阀门C值
    # 计算小炉阀门C值
    # 计算小炉阀门C值
    def calculate_small_valve_c_values(self, index):
        """计算小炉阀门的C计大和C计小"""
        try:
            print(f"开始计算小炉{index+1}的C值...")
            
            # 获取输入值
            if index >= len(self.small_valve_data) or index >= len(self.parent.furnace_data):
                print(f"索引{index}超出范围，无法计算C值")
                return
            
            # 获取小炉流量 - 修改为使用总流量
            max_flow_str = self.parent.furnace_data[index]['max_flow'].cget("text") if 'max_flow' in self.parent.furnace_data[index] else ""
            min_flow_str = self.parent.furnace_data[index]['min_flow'].cget("text") if 'min_flow' in self.parent.furnace_data[index] else ""
            
            print(f"小炉流量: 最大={max_flow_str}, 最小={min_flow_str}")
            
            # 获取压力
            pre_pressure_str = self.branch_valve_pre.get()
            post_pressure_str = self.branch_valve_post.get()
            
            print(f"压力: 前压力={pre_pressure_str}, 后压力={post_pressure_str}")
            
            # 获取温度
            temperature_str = self.temperature.get()
            
            print(f"温度: {temperature_str}")
            
            # 检查数据有效性 - 修改变量名
            if not max_flow_str or not min_flow_str or not pre_pressure_str or not post_pressure_str or not temperature_str:
                print(f"数据不完整，无法计算C值")
                return
            
            try:
                # 转换为浮点数 - 修改变量名
                max_flow = float(max_flow_str)
                min_flow = float(min_flow_str)
                pre_pressure = float(pre_pressure_str)
                post_pressure = float(post_pressure_str)
                temperature = float(temperature_str)
                
                # 检查数值是否有效
                if max_flow <= 0 or min_flow <= 0 or pre_pressure <= 0 or post_pressure <= 0 or pre_pressure <= post_pressure:
                    print(f"数值无效，无法计算C值")
                    return
                    
                # 计算压力差
                delta_p = (pre_pressure - post_pressure) * 10
                
                # 将压力单位从MPa转换为bar (1 MPa = 10 bar)
                pre_pressure_bar = pre_pressure * 10 + 1
                post_pressure_bar = post_pressure * 10 + 1
                
                # 计算C计大
                c_max = 1.167 * max_flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                    (1 - 0.46 * (delta_p) / (pre_pressure_bar)) / \
                    math.sqrt(delta_p * (pre_pressure_bar))
                
                # 计算C计小
                c_min = 1.167 * min_flow * math.sqrt(0.743 * (273 + temperature)) / 514 / \
                    (1 - 0.46 * (delta_p) / (pre_pressure_bar)) / \
                    math.sqrt(delta_p * (pre_pressure_bar))
                
                # 打印计算过程和结果
                print(f"压力差(bar): {delta_p}")
                print(f"前压力(bar): {pre_pressure_bar}")
                print(f"计算因子1: {1.167 * math.sqrt(0.743 * (273 + temperature))}")
                print(f"计算因子2: {1 - 0.46 * delta_p / pre_pressure_bar}")
                print(f"计算因子3: {math.sqrt(delta_p * pre_pressure_bar)}")
                print(f"计算结果: C计大={c_max:.4f}, C计小={c_min:.4f}")
                
                # 更新显示
                self.small_valve_data[index]['c_max'].set(f"{c_max:.2f}" if c_max > 0 else "")
                self.small_valve_data[index]['c_min'].set(f"{c_min:.2f}" if c_min > 0 else "")
                
                # 如果C选定已有值，自动计算K值
                self.calculate_small_valve_k_values(index)
                
            except ValueError as ve:
                print(f"转换数值出错: {str(ve)}")
            
        except Exception as e:
            print(f"计算小炉阀门C值时出错: {str(e)}")
            import traceback
            traceback.print_exc()
    # 【新增方法】计算小炉阀门K值
    def calculate_small_valve_k_values(self, index):
        """计算小炉阀门的K大和K小"""
        try:
            if index < len(self.small_valve_data):
                # 获取C值
                c_max_str = self.small_valve_data[index]['c_max'].get()
                c_min_str = self.small_valve_data[index]['c_min'].get()
                c_selected_str = self.small_valve_data[index]['c_selected'].get()
                
                # 检查输入是否有效
                if c_max_str and c_min_str and c_selected_str:
                    try:
                        c_max = float(c_max_str)
                        c_min = float(c_min_str)
                        c_selected = float(c_selected_str)
                        
                        # 检查C选定是否有效
                        if c_selected <= 0:
                            return
                        
                        # 计算K大
                        k_max = 1 + 0.68 * math.log10(c_max / c_selected)
                        
                        # 计算K小
                        k_min = 1 + 0.68 * math.log10(c_min / c_selected)
                        
                        # 更新显示
                        self.small_valve_data[index]['k_max'].set(f"{k_max:.2f}" if k_max > 0 else "")
                        self.small_valve_data[index]['k_min'].set(f"{k_min:.2f}" if k_min > 0 else "")
                        # 【修正】保存K值 - 移到这里，在成功计算后保存
                        self.save_small_valve_k_values(index)
                    except ValueError:
                        pass
        except Exception as e:
            print(f"计算小炉阀门K值时出错: {str(e)}") 
     # 【新增方法】保存小炉阀门C选定值
    def save_small_valve_c_selected(self, index):
        """保存小炉阀门C选定值"""
        try:
            if index < len(self.small_valve_data):
                # 获取C选定值
                c_selected = self.small_valve_data[index]['c_selected'].get()

                # 将值保存到parent直接管理的变量
                key = f"小炉{index+1}调节阀C选定"
                if not hasattr(self.parent, 'small_valve_c_values'):
                    self.parent.small_valve_c_values = {}

                # 直接设置父对象的变量
                self.parent.small_valve_c_values[key] = c_selected

                # 同时更新到persistent_data
                if index < len(self.small_valve_persistent_data):
                    self.small_valve_persistent_data[index]['c_selected'] = c_selected

                # 更新设备表中对应小炉的Cv值
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_small_valve_specification'):
                    self.parent.equipment_manager.update_small_valve_specification(index)
                    print(f"已触发小炉{index+1}设备表Cv值更新")

                # 立即保存
                self.parent.save_project_silent()

        except Exception as e:
            print(f"保存小炉阀门C选定值时出错: {str(e)}")
    
    # 【新增方法】保存小炉阀门K值
    def save_small_valve_k_values(self, index):
        """保存小炉阀门K大和K小值"""
        try:
            if index < len(self.small_valve_data) and index < len(self.small_valve_persistent_data):
                self.small_valve_persistent_data[index]['k_max'] = self.small_valve_data[index]['k_max'].get()
                self.small_valve_persistent_data[index]['k_min'] = self.small_valve_data[index]['k_min'].get()
        except Exception as e:
            print(f"保存小炉阀门K值时出错: {str(e)}")
    
    # 添加一个新方法，用于将调节阀窗口的管径同步回主界面
    def sync_to_main_diameter(self, *args):
        """同步调节阀计算窗口的总管阀前管径选取到主界面"""
        try:
            selected_diameter = self.parent.main_pre_selected.get()
            if selected_diameter and hasattr(self.parent, 'main_pre_selected'):
                self.parent.main_pre_selected.set(selected_diameter)
        except Exception as e:
            print(f"同步管径到主界面时出错: {str(e)}")
    
    def save_main_valve_diameter(self, *args):
        """保存主管调节阀阀门选取管径值"""
        try:
            if hasattr(self.parent, 'main_valve_diameter_value'):
                # 获取当前值并设置到父对象
                valve_diameter = self.main_valve_diameter.get()
                self.parent.main_valve_diameter_value.set(valve_diameter)
                
                # 立即调用设备表更新方法
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_valve_specification'):
                    self.parent.equipment_manager.update_valve_specification()
                    print(f"管径变更为{valve_diameter}，已触发设备表更新")
                
                # 静默保存项目
                self.parent.save_project_silent()
                print(f"保存主管调节阀阀门选取管径: {valve_diameter}")
        except Exception as e:
            print(f"保存主管调节阀阀门选取管径时出错: {str(e)}")
    
    def save_main_valve_core_size(self, *args):
        """保存主管调节阀阀门阀芯尺寸值"""
        try:
            if hasattr(self.parent, 'main_valve_core_size_value'):
                # 获取当前值并设置到父对象
                core_size = self.main_valve_core_size.get()
                self.parent.main_valve_core_size_value.set(core_size)
                
                # 立即调用设备表更新方法
                if hasattr(self.parent, 'equipment_manager') and hasattr(self.parent.equipment_manager, 'update_valve_specification'):
                    self.parent.equipment_manager.update_valve_specification()
                    print(f"阀芯尺寸变更为{core_size}，已触发设备表更新")
                
                # 静默保存项目
                self.parent.save_project_silent()
                print(f"保存主管调节阀阀门阀芯尺寸: {core_size}")
        except Exception as e:
            print(f"保存主管调节阀阀门阀芯尺寸时出错: {str(e)}")
    