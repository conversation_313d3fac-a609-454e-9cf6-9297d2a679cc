import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import docx
import math
from datetime import datetime
import json
import os
import sys
import traceback
import shutil
from self_operated_valve import SelfOperatedValve
from oxygen_lance import OxygenLanceCalculator
from compressed_air import CompressedAirCalculator  # 导入压缩空气计算类
from oxygen_pipe import OxygenPipeCalculator # 导入全氧窑氧气管道计算类
from valve_calculator import ValveCalculator # 导入调节阀门计算类
from equipment_manager import EquipmentManager# 设备表管理类
class GasCalculator:
    def __init__(self, root):
        self.root = root
        self.root.title("工艺天然气计算")
        
        
        # 【新增】窗口管理系统
        self.open_windows = {}  # 用于存储所有打开的窗口
        # 【阀门相关属性】确保所有阀门相关属性都被正确初始化
        self.release_valve_area = tk.StringVar(value="")
        self.release_valve_d0 = tk.StringVar(value="")   # 添加这个缺失的属性
        self.release_valve_dn = tk.StringVar(value="")
        # 【在此处修改】阀门计算器持久化变量正确初始化
        self.main_valve_c_selected_value = tk.StringVar(value="")
        
        self.main_valve_k_max_value = tk.StringVar(value="")
        self.main_valve_k_min_value = tk.StringVar(value="")
        self.release_valve_dn_value = tk.StringVar(value="")
        self.release_valve_area_value = tk.StringVar(value="")
        self.release_valve_d0_value = tk.StringVar(value="")
        self.release_valve_working_pressure_value = tk.StringVar(value="")
        self.release_valve_d0_value = tk.StringVar(value="")
        self.has_oxygen_lance = tk.StringVar(value="否")
        self.is_oxygen_kiln = tk.StringVar(value="否")
        # 初始化氧枪计算器
        self.oxygen_lance_calculator = OxygenLanceCalculator(self)
        # 初始化设备表管理类
        self.equipment_manager = EquipmentManager(self)
        # 初始化设备数据但不显示窗口
        self.equipment_manager.show_equipment_list(show_window=False)
        
        # 初始化应用程序目录和历史文件路径
        self.app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'GasCalculator')
        if not os.path.exists(self.app_data_dir):
            os.makedirs(self.app_data_dir)
        # 默认历史文件路径
        self.history_file = os.path.join(self.app_data_dir, 'history.json')

        # 加载配置文件中的history_file_path
        self.config_file = os.path.join(self.app_data_dir, 'config.json')
        self.load_config()

        # 根据配置文件设置历史文件路径
        if hasattr(self, 'custom_history_path') and self.custom_history_path and os.path.exists(os.path.dirname(self.custom_history_path)):
            self.history_file = self.custom_history_path

        # 初始化自力式阀计算器
        self.self_operated_valve = SelfOperatedValve(self)

        self.velocity_colors = {
        "normal": "#009900",  # 绿色 - 正常范围
        "warning": "#ff9900",  # 橙色 - 注意范围
        "danger": "#cc0000"   # 红色 - 危险范围
    }

        # 添加主题设置相关变量
        self.themes = ["default", "alt", "clam", "classic", "high_contrast", "eye_comfort"]
        self.current_theme = tk.StringVar(value="eye_comfort")
        self.theme_names = {
            "default": "默认主题",
            "alt": "备选主题",
            "clam": "简约主题",
            "classic": "经典主题",
            "high_contrast": "高对比度主题",
            "eye_comfort": "护眼模式"
        }

        # 设置默认主题
        style = ttk.Style()
        style.theme_use("default")  # 先应用默认主题
        self.apply_eye_comfort_theme(style)  # 再应用护眼模式

        self.small_valve_persistent_data = []  # 用于存储小炉阀门的C选定、K大、K小值、阀门选取管径、阀门阀芯尺寸值
        print("初始化small_valve_persistent_data")
        

        # 获取屏幕尺寸
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()

        # 设置窗口尺寸 - 【修改】增加窗口高度
        window_width = int(screen_width * 0.8)  # 增加窗口宽度
        window_height = int(screen_height * 1)  # 增加窗口高度


        # 计算窗口位置，使其居中显示
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2

        # 设置窗口大小和位置
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 设置窗口最大化
        # self.root.state('zoomed')  # 注释掉最大化设置

        # 创建底部按钮栏（放在最上面）
        #self.create_side_menu()

        # 创建主容器（水平分割）
        self.main_container = ttk.PanedWindow(root, orient=tk.HORIZONTAL)
        self.main_container.pack(fill=tk.BOTH, expand=True)

        # 创建左侧工作区域
        self.work_area = ttk.PanedWindow(self.main_container, orient=tk.VERTICAL)
        self.main_container.add(self.work_area, weight=3)  # 工作区占3份宽度

        # 创建右侧历史记录区域
        self.history_frame = ttk.LabelFrame(self.main_container, text="历史记录")
        self.main_container.add(self.history_frame, weight=1)  # 历史记录占1份宽度
        # 创建侧边菜单栏 (新增代码)
        self.create_side_menu()
        self.app_data_dir = os.path.join(os.environ.get('APPDATA', os.path.expanduser('~')), 'GasCalculator')
        if not os.path.exists(self.app_data_dir):
            os.makedirs(self.app_data_dir)
        # 默认历史文件路径
        self.history_file = os.path.join(self.app_data_dir, 'history.json')

        # 加载配置文件中的history_file_path
        self.config_file = os.path.join(self.app_data_dir, 'config.json')
        self.load_config()

        # 根据配置文件设置历史文件路径
        if hasattr(self, 'custom_history_path') and self.custom_history_path and os.path.exists(os.path.dirname(self.custom_history_path)):
            self.history_file = self.custom_history_path

        self.current_project = "未命名项目"
        self.furnace_frames = []

        # 初始化所有变量
        self.project_name = tk.StringVar(value="")
        self.project_code = tk.StringVar(value="")  # 【新增】工程代号变量
        self.daily_capacity = tk.StringVar(value="")  # 【新增】吨位变量

        self.furnace_count = tk.IntVar(value=1)
        self.project_type = tk.StringVar(value="")
        self.line_count = tk.IntVar(value=1)


        self.old_flow = tk.StringVar(value="")
        self.normal_flow = tk.StringVar(value="")
        # 添加这一行，当normal_flow变化时直接调用设备表更新方法
        self.normal_flow.trace_add("write", self.update_equipment_normal_flow)
        self.forming_flow = tk.StringVar(value="")
        self.edge_flow = tk.StringVar(value="")
        self.bypass_total_flow = tk.StringVar(value="")  # 【新增】支通路总流量变量
        
        self.inlet_pressure = tk.StringVar(value="")
        self.main_valve_pre = tk.StringVar(value="")
        self.main_valve_post = tk.StringVar(value="")
         # 添加对pressure_content方法中相关输入字段的trace
        self.main_valve_pre.trace_add("write", self.update_equipment_valve_pressure)
        self.main_valve_post.trace_add("write", self.update_equipment_valve_pressure)
        self.branch_valve_pre = tk.StringVar(value="")
        self.branch_valve_post = tk.StringVar(value="")
        self.main_valve_core_size_value = tk.StringVar(value="")
        self.main_valve_diameter_value = tk.StringVar()
        # 【新增】支通路压力变量
        self.bypass_main_valve_pre = tk.StringVar(value="")  # <-- 新增
        self.bypass_main_valve_post = tk.StringVar(value="")  # <-- 新增
        
        self.bypass_main_pre = tk.StringVar(value="")
        self.bypass_main_post = tk.StringVar(value="")
        self.bypass_branch_pre = tk.StringVar(value="")
        self.bypass_branch_post = tk.StringVar(value="")

        self.main_pre_diameter = tk.StringVar(value="")
        self.main_post_diameter = tk.StringVar(value="")
        self.bypass_main_pre_diameter = tk.StringVar(value="")
        self.bypass_main_post_diameter = tk.StringVar(value="")
        self.bypass_branch_pre_diameter = tk.StringVar(value="")
        self.bypass_branch_post_diameter = tk.StringVar(value="")


        self.main_velocity = tk.StringVar(value="")
        self.branch_velocity = tk.StringVar(value="")
        self.temperature = tk.StringVar(value="")
        # 初始化压缩空气相关变量
        self.air_pressure = tk.StringVar(value="")
        self.air_velocity = tk.StringVar(value="")
        self.air_continuous_total = tk.StringVar(value="")
        self.air_intermittent_total = tk.StringVar(value="")
        self.air_total_flow = tk.StringVar(value="")
        self.air_calculated_diameter = tk.StringVar(value="")
        self.air_standard_diameter = tk.StringVar(value="")
        self.air_actual_velocity = tk.StringVar(value="")
        # 初始化压缩空气计算器
        self.compressed_air_calculator = CompressedAirCalculator(self)
        # 在GasCalculator类的__init__方法中添加以下代码，位置在大约200行左右，其他变量初始化的地方
        # 初始化氧枪相关变量
        self.o2_flow = tk.StringVar(value="")
        self.o2_inlet_pressure = tk.StringVar(value="")
        self.o2_main_valve_pre = tk.StringVar(value="")
        self.o2_main_valve_post = tk.StringVar(value="")
        self.o2_branch_valve_pre = tk.StringVar(value="")
        self.o2_branch_valve_post = tk.StringVar(value="")
        self.o2_density = tk.StringVar(value="")
        self.o2_velocity = tk.StringVar(value="")
        self.o2_relief_valve_pressure = tk.StringVar(value="")
        self.o2_relief_valve_area = tk.StringVar(value="")
        self.o2_main_calc_diameter = tk.StringVar(value="")
        self.o2_main_selected_diameter = tk.StringVar(value="")
        self.o2_main_actual_velocity = tk.StringVar(value="")
        self.o2_branch_pre_calc_diameter = tk.StringVar(value="")
        self.o2_branch_pre_selected_diameter = tk.StringVar(value="")
        self.o2_branch_pre_actual_velocity = tk.StringVar(value="")
        self.o2_branch_post_calc_diameter = tk.StringVar(value="")
        self.o2_branch_post_selected_diameter = tk.StringVar(value="")
        self.o2_branch_post_actual_velocity = tk.StringVar(value="")
        self.o2_main_c_calculated = tk.StringVar(value="")
        self.o2_main_c_selected = tk.StringVar(value="")
        self.o2_main_k = tk.StringVar(value="")
        self.o2_branch_c_calculated = tk.StringVar(value="")
        self.o2_branch_c_selected = tk.StringVar(value="")
        self.o2_branch_k = tk.StringVar(value="")
        # 初始化氧枪计算器
        self.oxygen_lance_calculator = OxygenLanceCalculator(self)
        # 初始化氧气管计算器
        self.oxygen_pipe_calculator = None  # 初始化为None，延迟创建
        # 初始化调节阀门计算器
        self.valve_calculator = ValveCalculator(self)

        # 天然气数据
        self.ng_flow = tk.StringVar(value="")
        self.ng_inlet_pressure = tk.StringVar(value="")
        self.ng_main_valve_pre = tk.StringVar(value="")
        self.ng_main_valve_post = tk.StringVar(value="")
        self.ng_branch_valve_pre = tk.StringVar(value="")
        self.ng_branch_valve_post = tk.StringVar(value="")
        self.ng_density = tk.StringVar(value="")
        self.ng_velocity = tk.StringVar(value="")
        self.ng_main_calc_diameter = tk.StringVar(value="")
        self.ng_main_selected_diameter = tk.StringVar(value="")
        self.ng_main_actual_velocity = tk.StringVar(value="")
        self.ng_branch_pre_calc_diameter = tk.StringVar(value="")
        self.ng_branch_pre_selected_diameter = tk.StringVar(value="")
        self.ng_branch_pre_actual_velocity = tk.StringVar(value="")
        self.ng_branch_post_calc_diameter = tk.StringVar(value="")
        self.ng_branch_post_selected_diameter = tk.StringVar(value="")
        self.ng_branch_post_actual_velocity = tk.StringVar(value="")
        self.ng_main_c_calculated = tk.StringVar(value="")
        self.ng_main_c_selected = tk.StringVar(value="")
        self.ng_main_k = tk.StringVar(value="")
        self.ng_branch_c_calculated = tk.StringVar(value="")
        self.ng_branch_c_selected = tk.StringVar(value="")
        self.ng_branch_k = tk.StringVar(value="")

        # 添加触发绑定，可以放在__init__方法中或其他初始化方法中
        self.branch_valve_pre.trace_add("write", self.update_small_valve_pressure_flow)
        self.branch_valve_post.trace_add("write", self.update_small_valve_pressure_flow)

        # 对于小炉正常流量变化，需要在calculate_furnace_values方法的末尾添加调用
        # 在calculate_furnace_values方法末尾添加:
        # 更新设备表中小炉调节阀信息
        if hasattr(self, 'update_small_valve_pressure_flow'):
            self.update_small_valve_pressure_flow()

        # 初始化压缩空气用气明细变量
        self.air_input_vars = {
            "regenerator_purge": tk.StringVar(value=""),  # 蓄热室吹扫用气
            "gun_cooling": tk.StringVar(value=""),  # 喷枪冷却用气
            "feeder": tk.StringVar(value=""),  # 投料机用气
            "valve_tv": tk.StringVar(value=""),  # 阀及工业电视用气
            "cold_end": tk.StringVar(value=""),  # 冷端机组用气
            "annealing_ir": tk.StringVar(value=""),  # 退火窑及红外用气
            "branch_heating": tk.StringVar(value=""),  # 支通路加热用气
            "rolling_burn": tk.StringVar(value=""),  # 压延机烧边火用气
            "rolling_clean": tk.StringVar(value="")  # 压延机清理用气
        }

        # 创建界面
        self.create_main_layout()
        self.create_history_panel()
        # 加载窗口大小配置
        self.load_window_size_config()
        # 在__init__方法中的self.air_input_vars初始化后（约142行）添加
        self.saved_air_data = {}  # 用于保存压缩空气数据
        # 预先初始化压缩空气窗口的所有变量，即使窗口未打开
        self.air_branch1_description = tk.StringVar(value="")
        self.air_branch1_flow = tk.StringVar(value="")
        self.air_branch1_pressure = tk.StringVar(value="")
        self.air_branch1_diameter = tk.StringVar(value="")
        self.air_branch1_selected_diameter = tk.StringVar(value="")
        self.air_branch1_actual_velocity = tk.StringVar(value="")

        self.air_branch2_description = tk.StringVar(value="")
        self.air_branch2_flow = tk.StringVar(value="")
        self.air_branch2_pressure = tk.StringVar(value="")
        self.air_branch2_diameter = tk.StringVar(value="")
        self.air_branch2_selected_diameter = tk.StringVar(value="")
        self.air_branch2_actual_velocity = tk.StringVar(value="")

        self.air_branch3_description = tk.StringVar(value="")
        self.air_branch3_flow = tk.StringVar(value="")
        self.air_branch3_pressure = tk.StringVar(value="")
        self.air_branch3_diameter = tk.StringVar(value="")
        self.air_branch3_selected_diameter = tk.StringVar(value="")
        self.air_branch3_actual_velocity = tk.StringVar(value="")
         # 绑定变量变化事件
        self.main_valve_c_selected_value.trace_add("write", self.equipment_manager.update_valve_specification)
        self.main_valve_core_size_value.trace_add("write", self.equipment_manager.update_valve_specification)

        self.main_valve_c_selected_value.trace_add("write", self.equipment_manager.update_valve_c_selected)

        # 加载历史记录
        self.load_history()
        # 绑定主窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    # 添加一个新函数
    def backup_history_file(self):
        """创建历史文件的备份"""
        try:
            if os.path.exists(self.history_file):
                backup_dir = os.path.join(os.path.dirname(self.history_file), "备份")
                if not os.path.exists(backup_dir):
                    os.makedirs(backup_dir)

                today = datetime.now().strftime("%Y%m%d")
                backup_file = os.path.join(backup_dir, f"history_{today}.json")

                shutil.copy2(self.history_file, backup_file)
        except Exception as e:
            print(f"创建历史文件备份时出错: {str(e)}")
    # 添加加载配置文件的方法
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.custom_history_path = config.get('history_file_path', '')
            else:
                self.custom_history_path = ''
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")
            self.custom_history_path = ''
    def center_window(self, window):
        """使窗口在屏幕中居中显示"""
        window.update_idletasks()
        width = window.winfo_width()
        height = window.winfo_height()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f'{width}x{height}+{x}+{y}')

    # 添加保存配置文件的方法
    def save_config(self):
        """保存配置文件"""
        try:
            config = {}
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

            # 更新历史文件路径
            config['history_file_path'] = self.custom_history_path if hasattr(self, 'custom_history_path') else ''

            # 保存配置文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件出错: {str(e)}")

    def create_main_layout(self):
        """创建主界面布局"""
        # 创建图形界面区域
        self.create_tab_interface()

        # 创建小炉区
        self.create_furnace_section()

        # 创建输出区
        self.create_output_section()

    # 在GasCalculator类中添加 save_air_data 方法
    def save_air_data(self):
        """
        保存压缩空气计算数据到主程序中
        当压缩空气窗口打开时调用此方法以确保数据不丢失
        """
        try:
            if hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator:
                # 收集当前压缩空气数据
                air_data = self.compressed_air_calculator.collect_data()

                # 获取当前项目标识
                project_name = self.project_name.get()
                project_code = self.project_code.get()
                current_project_id = f"{project_name}_{project_code}"

                # 初始化为字典的字典（如果需要）
                if not hasattr(self, 'saved_air_data'):
                    self.saved_air_data = {}
                elif not isinstance(self.saved_air_data, dict):
                    self.saved_air_data = {}

                # 以项目标识为键，存储压缩空气数据
                self.saved_air_data[current_project_id] = air_data
                print(f"压缩空气数据已保存到主程序，项目ID: {current_project_id}")
                return True
        except Exception as e:
            print(f"保存压缩空气数据时出错: {str(e)}")
            traceback.print_exc()
        return False
    def save_project(self, show_message=True):
        """保存项目功能"""
        try:
            # 备份历史文件
            self.backup_history_file()

            # 获取压缩空气数据
            air_data = {}
            # 获取当前项目标识
            project_name = self.project_name.get()
            project_code = self.project_code.get()
            current_project_id = f"{project_name}_{project_code}"

            # 1. 首先尝试从打开的窗口获取最新数据
            if hasattr(self, 'air_window') and self.air_window and self.air_window.winfo_exists():
                try:
                    # 保存当前压缩空气数据到self.saved_air_data[current_project_id]
                    self.save_air_data()
                except Exception as e:
                    print(f"同步压缩空气窗口数据出错: {str(e)}")
                    traceback.print_exc()

            # 2. 使用保存的数据或直接从计算器获取
            if hasattr(self, 'saved_air_data') and isinstance(self.saved_air_data, dict) and current_project_id in self.saved_air_data:
                air_data = self.saved_air_data[current_project_id]
                print(f"使用已保存的{current_project_id}项目的压缩空气数据")
            elif hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator:
                try:
                    air_data = self.compressed_air_calculator.collect_data()
                    # 确保数据与当前项目关联
                    if hasattr(self, 'saved_air_data'):
                        if not isinstance(self.saved_air_data, dict):
                            self.saved_air_data = {}
                        self.saved_air_data[current_project_id] = air_data
                    print(f"已重新获取{current_project_id}项目的压缩空气数据")
                except Exception as e:
                    print(f"获取压缩空气数据时出错: {str(e)}")
                    traceback.print_exc()
            

            # 收集项目数据
            project_data = {
                "工程名称": self.project_name.get(),
                "工程代号": self.project_code.get(),
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                
            }

            # 合并压缩空气数据
            if air_data:
                for key, value in air_data.items():
                    project_data[key] = value

        except Exception as e:
            print(f"保存项目数据时出错: {str(e)}")
            traceback.print_exc()
            if show_message:
                messagebox.showerror("保存失败", f"保存项目数据时出错: {str(e)}")
            return False
        # 添加在构建项目数据前的调试输出
        print(f"保存项目时，是否有0#氧枪: {self.has_oxygen_lance.get()}, 是否是全氧窑: {self.is_oxygen_kiln.get()}")
        # 在保存项目前，如果有0#氧枪，先同步氧枪计算器数据
        if self.has_oxygen_lance.get() == "是":
            try:
                # 确保氧枪计算器的数据被保存到内部变量中
                self.oxygen_lance_calculator.save_data(show_message=False)
                print("已同步0#氧枪计算器数据")
            except Exception as e:
                print(f"同步氧枪计算器数据时出错: {str(e)}")
                traceback.print_exc()

        # 如果历史记录文件存在，先读取当前项目的历史数据
        self.backup_history_file()
        old_data = {}
        # 【重要】保存项目前，读取历史文件中的氧枪数据
        oxygen_lance_data = {}
        oxygen_lance_ng_data = {}
        # 读取历史文件中当前项目的数据，确保不丢失氧枪数据
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    for record in history:
                        if record.get("工程名称") == self.project_name.get() and record.get("工程代号") == self.project_code.get():
                            # 保存氧枪相关数据
                            if "氧枪氧气数据" in record:
                                oxygen_lance_data = record["氧枪氧气数据"]
                            if "氧枪天然气数据" in record:
                                oxygen_lance_ng_data = record["氧枪天然气数据"]
                            # 保存其他数据
                            for key in record.keys():
                                if key.startswith("氧枪_"):
                                    old_data[key] = record[key]

                            # 确保保存"是否有0#氧枪"字段
                            if "是否有0#氧枪" in record:
                                old_data["是否有0#氧枪"] = record["是否有0#氧枪"]
                            break
            except Exception as e:
                print(f"读取历史氧枪数据出错: {str(e)}")
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    for record in history:
                        if record.get("工程名称") == self.project_name.get() and record.get("工程代号") == self.project_code.get():
                            # 保存所有压缩空气相关数据（顶层数据）
                            for key in list(record.keys()):
                                if ("压缩空气" in key or
                                    key.startswith("蓄热室吹扫用气") or
                                    key.startswith("喷枪冷却用气") or
                                    key.startswith("投料机用气") or
                                    key.startswith("阀及工业电视用气") or
                                    key.startswith("冷端机组用气") or
                                    key.startswith("退火窑及红外用气") or
                                    key.startswith("支通路加热用气") or
                                    key.startswith("压延机烧边火用气") or
                                    key.startswith("压延机清理用气") or
                                    key == "连续用气总量(Nm³/h)" or
                                    key == "间歇用气总量(Nm³/h)" or
                                    key == "总用气量(Nm³/h)" or
                                    key == "计算管径(mm)" or
                                    key == "选取管径(mm)" or
                                    key == "实际流速(m/s)" or
                                    key == "进车间压力(MPa)" or
                                    key == "设计流速(m/s)" or
                                    key.startswith("自力式阀") or  # 修改此处，确保以自力式阀开头的键都保存
                                    key.startswith("支管") or
                                     # 添加全氧窑氧气管道计算相关的数据键
                                    key.startswith("氧气") or
                                    key.startswith("天然气") or
                                    key == "窑老期流量(Nm³/h)" or
                                    key == "氧气小炉数据" or

                                    key == "氧气介质密度" or
                                    key == "氧气总管C计大" or
                                    key == "氧气总管C计小" or
                                    key == "氧气总管C选定" or
                                    key == "氧气总管K大" or
                                    key == "氧气总管K小" or
                                    key == "计算类型"):
                                    old_data[key] = record[key]

                            break
            except Exception as e:
                print(f"读取历史数据出错: {str(e)}")
        try:
            # 获取当前时间
            history_dir = os.path.dirname(self.history_file)
            if not os.path.exists(history_dir):
                os.makedirs(history_dir)
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # 获取工程名称和工程代号
            project_name = self.project_name.get()
            project_code = self.project_code.get()
            # 明确设置氧枪状态，不依赖历史数据
            project_data["是否有0#氧枪"] = self.has_oxygen_lance.get()
            # 检查是否输入了工程名称或工程代号
            if not project_name and not project_code:
                messagebox.showwarning("警告", "请至少输入工程名称或工程代号")
                return
            if hasattr(self, 'air_window') and self.air_window and self.air_window.winfo_exists():
                # 先保存当前压缩空气数据到独立存储
                self.save_air_data()
            # 收集项目数据

            project_data = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "工程名称": self.project_name.get(),
                "工程代号": self.project_code.get(),
                "项目类型": self.project_type.get(),
                "吨位": self.daily_capacity.get(),
                "小炉数": self.furnace_count.get(),
                "一窑几线": self.line_count.get(),
                 # ...其他字段...
                "是否有0#氧枪": self.has_oxygen_lance.get() if hasattr(self, 'has_oxygen_lance') else "否",
                "是否是全氧窑": self.is_oxygen_kiln.get() if hasattr(self, 'is_oxygen_kiln') else "否",
                # 【新增】直接保存用气需求数据到顶层
                "进车间压力(MPa)": self.air_pressure.get(),
                "设计流速(m/s)": self.air_velocity.get(),
                "连续用气总量(Nm³/h)": self.air_continuous_total.get(),
                "间歇用气总量(Nm³/h)": self.air_intermittent_total.get(),
                "总用气量(Nm³/h)": self.air_total_flow.get(),
                "计算管径(mm)": self.air_calculated_diameter.get(),
                "选取管径(mm)": self.air_standard_diameter.get(),
                "实际流速(m/s)": self.air_actual_velocity.get(),
                # 添加放散阀相关字段到顶层
                "放散阀工作压力": self.release_valve_working_pressure_value.get(),
                "放散阀反算d0": self.release_valve_d0_value.get(),
                "放散阀选取DN": self.release_valve_dn_value.get(),
                "放散阀截面积A": self.release_valve_area_value.get(),
                # 添加主管调节阀C选定到顶层
                "主管调节阀C选定": self.main_valve_c_selected_value.get(),
                "主管调节阀K最大值": self.main_valve_k_max_value.get(),
                "主管调节阀阀芯尺寸": self.main_valve_core_size_value.get(),
                "主管调节阀K最小值": self.main_valve_k_min_value.get(),
                 "主管调节阀C计算大": self.main_valve_c_large.get() if hasattr(self, 'main_valve_c_large') else "",
                "主管调节阀C计算小": self.main_valve_c_small.get() if hasattr(self, 'main_valve_c_small') else "",
                "主管调节阀管径": self.main_valve_diameter_value.get(),  # 添加到主管调节阀相关字段部分

                # 添加全氧窑氧气管道计算数据到顶层
                "氧气正常流量(Nm³/h)": self.oxygen_flow.get() if hasattr(self, 'oxygen_flow') else "",
                "窑老期流量(Nm³/h)": self.oxygen_old_flow.get() if hasattr(self, 'oxygen_old_flow') else "",
                "氧气进车间压力(MPa)": self.oxygen_inlet_pressure.get() if hasattr(self, 'oxygen_inlet_pressure') else "",
                "氧气总管调节阀前压力(MPa)": self.oxygen_main_pre_pressure.get() if hasattr(self, 'oxygen_main_pre_pressure') else "",
                "氧气总管调节阀后压力(MPa)": self.oxygen_main_post_pressure.get() if hasattr(self, 'oxygen_main_post_pressure') else "",
                "氧气支管阀前压力(MPa)": self.oxygen_branch_pre_pressure.get() if hasattr(self, 'oxygen_branch_pre_pressure') else "",
                "氧气支管阀后压力(MPa)": self.oxygen_branch_post_pressure.get() if hasattr(self, 'oxygen_branch_post_pressure') else "",
                "氧气设计流速(m/s)": self.oxygen_velocity.get() if hasattr(self, 'oxygen_velocity') else "",
                "氧气设计温度(℃)": self.oxygen_temperature.get() if hasattr(self, 'oxygen_temperature') else "",
                "氧气工作状态下流量(Nm³/h)": self.oxygen_working_flow.get() if hasattr(self, 'oxygen_working_flow') else "",
                "氧气窑老期工作状态下流量(Nm³/h)": self.oxygen_working_old_flow.get() if hasattr(self, 'oxygen_working_old_flow') else "",
                "氧气进车间总管管径(mm)": self.oxygen_main_diameter.get() if hasattr(self, 'oxygen_main_diameter') else "",
                "氧气选取总管阀前管径(mm)": self.oxygen_selected_diameter.get() if hasattr(self, 'oxygen_selected_diameter') else "",
                "氧气反算总管阀前流速(m/s)": self.oxygen_actual_velocity.get() if hasattr(self, 'oxygen_actual_velocity') else "",
                "氧气总管调节阀后管径(mm)": self.oxygen_main_post_calc_diameter.get() if hasattr(self, 'oxygen_main_post_calc_diameter') else "",
                "氧气选取总管阀后管径(mm)": self.oxygen_main_post_selected_diameter.get() if hasattr(self, 'oxygen_main_post_selected_diameter') else "",
                "氧气反算总管阀后流速(m/s)": self.oxygen_main_post_actual_velocity.get() if hasattr(self, 'oxygen_main_post_actual_velocity') else "",

                # 氧气总管调节阀计算数据
                "氧气总管C计大": self.oxygen_main_c_large.get() if hasattr(self, 'oxygen_main_c_large') else "",
                "氧气总管C计小": self.oxygen_main_c_small.get() if hasattr(self, 'oxygen_main_c_small') else "",
                "氧气总管C选定": self.oxygen_main_c_selected.get() if hasattr(self, 'oxygen_main_c_selected') else "",
                "氧气总管K大": self.oxygen_main_k_large.get() if hasattr(self, 'oxygen_main_k_large') else "",
                "氧气总管K小": self.oxygen_main_k_small.get() if hasattr(self, 'oxygen_main_k_small') else "",
                "氧气介质密度": self.oxygen_density.get() if hasattr(self, 'oxygen_density') else "",

                # 小炉区氧气数据
                "氧气小炉数据": [
                    {
                        "小炉编号": f"小炉{idx+1}",
                        "平均热负荷": furnace['heat_load'].get() if 'heat_load' in furnace else "",
                        "浮动值": furnace['float_value'].get() if 'float_value' in furnace else "",
                        "阀前流量": furnace['pre_flow'].get() if 'pre_flow' in furnace else "",
                        "阀前计算管径": furnace['pre_calc_diameter'].get() if 'pre_calc_diameter' in furnace else "",
                        "阀前选取管径": furnace['pre_selected_diameter'].get() if 'pre_selected_diameter' in furnace else "",
                        "阀前反算流速": furnace['pre_actual_velocity'].get() if 'pre_actual_velocity' in furnace else "",
                        "阀后流量": furnace['post_flow'].get() if 'post_flow' in furnace else "",
                        "阀后计算管径": furnace['post_calc_diameter'].get() if 'post_calc_diameter' in furnace else "",
                        "阀后选取管径": furnace['post_selected_diameter'].get() if 'post_selected_diameter' in furnace else "",
                        "阀后反算流速": furnace['post_actual_velocity'].get() if 'post_actual_velocity' in furnace else "",
                        "C计大": furnace.get('c_large', tk.StringVar()).get() if 'c_large' in furnace else "",
                        "C计小": furnace.get('c_small', tk.StringVar()).get() if 'c_small' in furnace else "",
                        "C选定": furnace.get('c_selected', tk.StringVar()).get() if 'c_selected' in furnace else "",
                        "K大": furnace.get('k_large', tk.StringVar()).get() if 'k_large' in furnace else "",
                        "K小": furnace.get('k_small', tk.StringVar()).get() if 'k_small' in furnace else ""
                    } for idx, furnace in enumerate(self.oxygen_furnace_data) if hasattr(self, 'oxygen_furnace_data') and self.oxygen_furnace_data
                ] if hasattr(self, 'oxygen_furnace_data') and self.oxygen_furnace_data else [],


                "流量数据": {
                    # 流量数据内容不变
                },
                "流量数据": {
                    "窑老期流量": self.old_flow.get(),
                    "正常生产时流量": self.normal_flow.get(),
                    "成型室加热流量": self.forming_flow.get(),
                    "边火加热流量": self.edge_flow.get(),
                    "支通路总流量": self.bypass_total_flow.get()
                },
                "压力数据": {
                    "进车间压力": self.inlet_pressure.get(),
                    "总管调节阀前压力": self.main_valve_pre.get(),
                    "总管调节阀后压力": self.main_valve_post.get(),
                    "小炉支管调节阀前压力": self.branch_valve_pre.get(),
                    "小炉支管调节阀后压力": self.branch_valve_post.get(),
                    "支通路总管阀前压力": self.bypass_main_valve_pre.get(),
                    "支通路总管阀后压力": self.bypass_main_valve_post.get(),
                    "边火阀前压力": self.bypass_vars['branch_pre']['pressure'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else "",
                    "边火阀后压力": self.bypass_vars['branch_post']['pressure'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else ""
                },
                "管径数据": {
                    "总管阀前管径": self.main_pre_diameter.get(),
                    "总管阀后管径": self.main_post_diameter.get(),
                    "支通路总管阀前管径": self.bypass_main_pre_diameter.get(),
                    "支通路总管阀后管径": self.bypass_main_post_diameter.get(),
                    "支通路支管阀前管径": self.bypass_vars['branch_pre']['selected'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else "",
                    "支通路支管阀后管径": self.bypass_vars['branch_post']['selected'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else ""
                },
                "流速温度数据": {
                    "天然气总管流速": self.main_velocity.get(),
                    "小炉支管流速": self.branch_velocity.get(),
                    "设计温度": self.temperature.get()
                },
                "小炉数据": [],
                "main_valve_c_selected": self.main_valve_c_selected_value.get(),
                "main_valve_k_max": self.main_valve_k_max_value.get(),
                "main_valve_k_min": self.main_valve_k_min_value.get(),
                "release_valve_dn": self.release_valve_dn_value.get(),
                "release_valve_working_pressure": self.release_valve_working_pressure_value.get(),  # 同时保存旧格式字段
                "release_valve_d0": self.release_valve_d0_value.get(),  # 同时保存旧格式字段
                "small_valve_data": self.small_valve_persistent_data,
            }
            # 【新增】保存用气明细数据到顶层
            if hasattr(self, 'air_input_vars'):
                project_data["蓄热室吹扫用气(间歇Nm³/h)"] = self.air_input_vars["regenerator_purge"].get()
                project_data["喷枪冷却用气(连续Nm³/h)"] = self.air_input_vars["gun_cooling"].get()
                project_data["投料机用气(连续Nm³/h)"] = self.air_input_vars["feeder"].get()
                project_data["阀及工业电视用气(连续Nm³/h)"] = self.air_input_vars["valve_tv"].get()
                project_data["冷端机组用气(连续Nm³/h)"] = self.air_input_vars["cold_end"].get()
                project_data["退火窑及红外用气(连续Nm³/h)"] = self.air_input_vars["annealing_ir"].get()
                project_data["支通路加热用气(间歇Nm³/h)"] = self.air_input_vars["branch_heating"].get()
                project_data["压延机烧边火用气(连续Nm³/h)"] = self.air_input_vars["rolling_burn"].get()
                project_data["压延机清理用气(间歇Nm³/h)"] = self.air_input_vars["rolling_clean"].get()
            # 获取压缩空气数据
            air_data = {}
            # 1. 首先尝试从打开的窗口获取最新数据
            if hasattr(self, 'air_window') and self.air_window and self.air_window.winfo_exists():
                try:
                    # 保存当前压缩空气数据到self.saved_air_data
                    self.save_air_data()
                except Exception as e:
                    print(f"同步压缩空气窗口数据出错: {str(e)}")
                    traceback.print_exc()

            # 2. 使用保存的数据或直接从计算器获取
            if hasattr(self, 'saved_air_data') and self.saved_air_data:
                air_data = self.saved_air_data
                # 【新增】标准化键名 - 确保使用标准格式的键
                if "进车间压力" in air_data and "进车间压力(MPa)" not in air_data:
                    air_data["进车间压力(MPa)"] = air_data["进车间压力"]
                if "设计流速" in air_data and "设计流速(m/s)" not in air_data:
                    air_data["设计流速(m/s)"] = air_data["设计流速"]
                if "连续用气总量" in air_data and "连续用气总量(Nm³/h)" not in air_data:
                    air_data["连续用气总量(Nm³/h)"] = air_data["连续用气总量"]
                if "间歇用气总量" in air_data and "间歇用气总量(Nm³/h)" not in air_data:
                    air_data["间歇用气总量(Nm³/h)"] = air_data["间歇用气总量"]
                if "总用气量" in air_data and "总用气量(Nm³/h)" not in air_data:
                    air_data["总用气量(Nm³/h)"] = air_data["总用气量"]
                if "计算管径" in air_data and "计算管径(mm)" not in air_data:
                    air_data["计算管径(mm)"] = air_data["计算管径"]
                if "选取管径" in air_data and "选取管径(mm)" not in air_data:
                    air_data["选取管径(mm)"] = air_data["选取管径"]
                if "实际流速" in air_data and "实际流速(m/s)" not in air_data:
                    air_data["实际流速(m/s)"] = air_data["实际流速"]

                print("使用已保存的压缩空气数据")
            elif hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator:
                try:
                    air_data = self.compressed_air_calculator.collect_data()
                    # 【新增】保存到saved_air_data确保持久化
                    self.saved_air_data = air_data
                    print(f"已重新获取压缩空气数据")
                except Exception as e:
                    print(f"获取压缩空气数据时出错: {str(e)}")
                    traceback.print_exc()
            # 【重要】如果有0#氧枪，确保将历史数据和新数据合并
            if self.has_oxygen_lance.get() == "是":
                # 首先保留历史文件中的氧枪数据
                if oxygen_lance_data:
                    project_data["氧枪氧气数据"] = oxygen_lance_data
                if oxygen_lance_ng_data:
                    project_data["氧枪天然气数据"] = oxygen_lance_ng_data

                # 再从old_data中恢复所有氧枪前缀的数据
                for key, value in old_data.items():
                    if key.startswith("氧枪_") and key not in project_data:
                        project_data[key] = value

                # 将当前氧枪计算器的数据添加到项目数据中
                if hasattr(self.oxygen_lance_calculator, 'o2_flow'):
                    o2_data = self.oxygen_lance_calculator.get_oxygen_data()
                    ng_data = self.oxygen_lance_calculator.get_natural_gas_data()

                    # 更新项目数据
                    project_data["氧枪氧气数据"] = o2_data
                    project_data["氧枪天然气数据"] = ng_data

                    # 使用"氧枪_"前缀保存氧气数据
                    for key, value in o2_data.items():
                        project_data[f"氧枪_氧气{key}"] = value

                    # 使用"氧枪_"前缀保存天然气数据
                    for key, value in ng_data.items():
                        project_data[f"氧枪_天然气{key}"] = value

            # 保留旧数据中的其他信息
            if old_data:
                for key, value in old_data.items():
                    # 避免覆盖新的氧枪数据
                    if key.startswith("氧枪_") and self.has_oxygen_lance.get() == "是":
                        continue
                    # 保留其他数据
                    if key not in project_data:
                        project_data[key] = value
            # 【重要】处理压缩空气支管数据
            # 获取压缩空气支管数量，如果没有相关属性则默认为3（兼容旧版本）
            air_branch_count = getattr(self, 'air_branch_count', 3) if hasattr(self, 'air_branch_count') else 3

            # 初始化一个列表用于存储所有支管数据
            branch_data_list = []

            # 使用循环处理所有支管
            for i in range(1, air_branch_count + 1):
                # 检查是否存在该支管的描述属性
                if hasattr(self, f'air_branch{i}_description') and getattr(self, f'air_branch{i}_description').get():
                    # 先保存所有字段到顶层
                    project_data[f"压缩空气支管{i}描述"] = getattr(self, f'air_branch{i}_description').get()
                    project_data[f"压缩空气支管{i}用气量(Nm³/h)"] = getattr(self, f'air_branch{i}_flow').get()
                    project_data[f"压缩空气支管{i}压力(MPa)"] = getattr(self, f'air_branch{i}_pressure').get()
                    project_data[f"压缩空气支管{i}计算管径(mm)"] = getattr(self, f'air_branch{i}_diameter').get()
                    project_data[f"压缩空气支管{i}选取管径(mm)"] = getattr(self, f'air_branch{i}_selected_diameter').get()
                    project_data[f"压缩空气支管{i}实际流速(m/s)"] = getattr(self, f'air_branch{i}_actual_velocity').get()

                    # 同时也保存到压缩空气支管i数据字典中
                    branch_data = {
                        "支管编号": i,
                        "支管描述": getattr(self, f'air_branch{i}_description').get(),
                        "流量": getattr(self, f'air_branch{i}_flow').get(),
                        "压力": getattr(self, f'air_branch{i}_pressure').get(),
                        "计算管径": getattr(self, f'air_branch{i}_diameter').get(),
                        "选取管径": getattr(self, f'air_branch{i}_selected_diameter').get(),
                        "实际流速": getattr(self, f'air_branch{i}_actual_velocity').get()
                    }
                    branch_data_list.append(branch_data)

                    # 也单独保存一个完整的字典
                    project_data[f"压缩空气支管{i}数据"] = branch_data

            # 保存所有支管数据到一个列表中
            if branch_data_list:
                project_data["压缩空气支管数据"] = branch_data_list
            # 支持从CompressedAirCalculator中获取更多的支管数据
            if hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator:
                try:
                    # 获取计算器实例
                    air_calc = self.compressed_air_calculator

                    # 如果计算器有branch_data属性，尝试从中获取支管数据
                    if hasattr(air_calc, 'branch_data') and air_calc.branch_data:
                        # 获取已保存的支管编号
                        existing_ids = [b.get("支管编号") for b in branch_data_list]

                        # 遍历计算器中的支管数据
                        for idx, branch_vars in air_calc.branch_data.items():
                            # 如果该支管尚未保存，则添加到branch_data_list
                            if idx not in existing_ids:
                                branch_data = {
                                    "支管编号": idx,
                                    "支管描述": branch_vars["description"].get(),
                                    "流量": branch_vars["flow"].get(),
                                    "压力": branch_vars["pressure"].get(),
                                    "计算管径": branch_vars["calculated_diameter"].get(),
                                    "选取管径": branch_vars["selected_diameter"].get(),
                                    "实际流速": branch_vars["actual_velocity"].get()
                                }
                                branch_data_list.append(branch_data)

                                # 也保存到顶层
                                project_data[f"压缩空气支管{idx}描述"] = branch_vars["description"].get()
                                project_data[f"压缩空气支管{idx}用气量(Nm³/h)"] = branch_vars["flow"].get()
                                project_data[f"压缩空气支管{idx}压力(MPa)"] = branch_vars["pressure"].get()
                                project_data[f"压缩空气支管{idx}计算管径(mm)"] = branch_vars["calculated_diameter"].get()
                                project_data[f"压缩空气支管{idx}选取管径(mm)"] = branch_vars["selected_diameter"].get()
                                project_data[f"压缩空气支管{idx}实际流速(m/s)"] = branch_vars["actual_velocity"].get()

                                # 保存完整数据字典
                                project_data[f"压缩空气支管{idx}数据"] = branch_data

                        # 更新压缩空气支管数据列表
                        if branch_data_list:
                            project_data["压缩空气支管数据"] = branch_data_list
                except Exception as e:
                    print(f"获取压缩空气计算器数据时出错: {str(e)}")
                    traceback.print_exc()

            # 设置计算类型（如果有压缩空气数据）
            if (hasattr(self, 'air_pressure') and self.air_pressure.get()) or branch_data_list:
                project_data["计算类型"] = "压缩空气管道计算"


             # 保存其他标签页数据
            if hasattr(self, 'valve_tab_count'):
                print(f"保存项目时，当前有{self.valve_tab_count}个自力式阀标签页")
                for i in range(2, self.valve_tab_count + 1):
                    if hasattr(self, f"valve{i}_description"):
                        # 先保存所有字段到顶层
                        project_data[f"自力式阀{i}描述"] = getattr(self, f"valve{i}_description").get()
                        project_data[f"自力式阀{i}最大流量(Nm³/h)"] = getattr(self, f"valve{i}_max_flow").get()
                        project_data[f"自力式阀{i}介质"] = getattr(self, f"valve{i}_medium").get()
                        project_data[f"自力式阀{i}介质密度(kg/m³)"] = getattr(self, f"valve{i}_density").get()
                        project_data[f"自力式阀{i}设计温度(℃)"] = getattr(self, f"valve{i}_temperature").get()
                        project_data[f"自力式阀{i}阀前压力(MPa)"] = getattr(self, f"valve{i}_pre_pressure").get()
                        project_data[f"自力式阀{i}阀后压力(MPa)"] = getattr(self, f"valve{i}_post_pressure").get()
                        project_data[f"自力式阀{i}C计算"] = getattr(self, f"valve{i}_c_calculated").get()
                        project_data[f"自力式阀{i}C选定"] = getattr(self, f"valve{i}_c_selected").get()
                        project_data[f"自力式阀{i}开度K(%)"] = getattr(self, f"valve{i}_k_percent").get()

                        # 同时也保存到自力式阀i数据字典中
                        valve_data = {
                            f"自力式阀{i}描述": getattr(self, f"valve{i}_description").get(),
                            "最大流量(Nm³/h)": getattr(self, f"valve{i}_max_flow").get(),
                            "介质": getattr(self, f"valve{i}_medium").get(),
                            "介质密度(kg/m³)": getattr(self, f"valve{i}_density").get(),
                            "设计温度(℃)": getattr(self, f"valve{i}_temperature").get(),
                            "阀前压力(MPa)": getattr(self, f"valve{i}_pre_pressure").get(),
                            "阀后压力(MPa)": getattr(self, f"valve{i}_post_pressure").get(),
                            "C计算": getattr(self, f"valve{i}_c_calculated").get(),
                            "C选定": getattr(self, f"valve{i}_c_selected").get(),
                            "开度K(%)": getattr(self, f"valve{i}_k_percent").get()
                        }
                        project_data[f"自力式阀{i}数据"] = valve_data

            # 【最关键修改】明确保存天然气栏数据
            if self.has_oxygen_lance.get() == "是":
                # 天然气数据 - 始终添加到项目数据根级别，无论窗口是否打开
                if hasattr(self, 'ng_flow'):
                    project_data["天然气流量(Nm³/h)"] = self.ng_flow.get()
                if hasattr(self, 'ng_inlet_pressure'):
                    project_data["天然气进车间压力(MPa)"] = self.ng_inlet_pressure.get()
                if hasattr(self, 'ng_main_valve_pre'):
                    project_data["天然气主管阀前压力(MPa)"] = self.ng_main_valve_pre.get()
                if hasattr(self, 'ng_main_valve_post'):
                    project_data["天然气主管阀后压力(MPa)"] = self.ng_main_valve_post.get()
                if hasattr(self, 'ng_branch_valve_pre'):
                    project_data["天然气支管阀前压力(MPa)"] = self.ng_branch_valve_pre.get()
                if hasattr(self, 'ng_branch_valve_post'):
                    project_data["天然气支管阀后压力(MPa)"] = self.ng_branch_valve_post.get()
                if hasattr(self, 'ng_density'):
                    project_data["天然气密度(kg/m³)"] = self.ng_density.get()
                if hasattr(self, 'ng_velocity'):
                    project_data["天然气流速(m/s)"] = self.ng_velocity.get()

                if hasattr(self, 'ng_main_calc_diameter'):
                    project_data["天然气主管计算管径"] = self.ng_main_calc_diameter.get()
                if hasattr(self, 'ng_main_selected_diameter'):
                    project_data["天然气选取主管管径"] = self.ng_main_selected_diameter.get()
                if hasattr(self, 'ng_main_actual_velocity'):
                    project_data["天然气反算主管流速"] = self.ng_main_actual_velocity.get()
                if hasattr(self, 'ng_branch_pre_calc_diameter'):
                    project_data["天然气支管计算阀前管径"] = self.ng_branch_pre_calc_diameter.get()
                if hasattr(self, 'ng_branch_pre_selected_diameter'):
                    project_data["天然气选取支管阀前管径"] = self.ng_branch_pre_selected_diameter.get()
                if hasattr(self, 'ng_branch_pre_actual_velocity'):
                    project_data["天然气反算支管阀前流速"] = self.ng_branch_pre_actual_velocity.get()
                if hasattr(self, 'ng_branch_post_calc_diameter'):
                    project_data["天然气支管计算阀后管径"] = self.ng_branch_post_calc_diameter.get()
                if hasattr(self, 'ng_branch_post_selected_diameter'):
                    project_data["天然气选取支管阀后管径"] = self.ng_branch_post_selected_diameter.get()
                if hasattr(self, 'ng_branch_post_actual_velocity'):
                    project_data["天然气反算支管阀后流速"] = self.ng_branch_post_actual_velocity.get()

                if hasattr(self, 'ng_main_c_calculated'):
                    project_data["天然气主管C计"] = self.ng_main_c_calculated.get()
                if hasattr(self, 'ng_main_c_selected'):
                    project_data["天然气主管C选定"] = self.ng_main_c_selected.get()
                if hasattr(self, 'ng_main_k'):
                    project_data["天然气主管K"] = self.ng_main_k.get()
                if hasattr(self, 'ng_branch_c_calculated'):
                    project_data["天然气支管C计"] = self.ng_branch_c_calculated.get()
                if hasattr(self, 'ng_branch_c_selected'):
                    project_data["天然气支管C选定"] = self.ng_branch_c_selected.get()
                if hasattr(self, 'ng_branch_k'):
                    project_data["天然气支管K"] = self.ng_branch_k.get()

            if old_data:
                for key, value in old_data.items():
                    # 将压缩空气数据和自力式阀数据直接添加到项目的最顶层
                    if ("压缩空气" in key or
                        key.startswith("蓄热室吹扫用气") or
                        key.startswith("喷枪冷却用气") or
                        key.startswith("投料机用气") or
                        key.startswith("阀及工业电视用气") or
                        key.startswith("冷端机组用气") or
                        key.startswith("退火窑及红外用气") or
                        key.startswith("支通路加热用气") or
                        key.startswith("压延机烧边火用气") or
                        key.startswith("压延机清理用气") or
                        key == "连续用气总量(Nm³/h)" or
                        key == "间歇用气总量(Nm³/h)" or
                        key == "总用气量(Nm³/h)" or
                        key == "计算管径(mm)" or
                        key == "选取管径(mm)" or
                        key == "实际流速(m/s)" or
                        key == "进车间压力(MPa)" or
                        key.startswith("支管") or  # 确保旧版支管数据被保留
                        key == "设计流速(m/s)" or
                        key.startswith("自力式阀") or  # 保留所有以自力式阀开头的键
                        # 添加全氧窑氧气管道计算相关的数据键
                        key.startswith("氧气") or
                        key == "窑老期流量(Nm³/h)" or
                        key == "氧气小炉数据" or
                        key == "是否有0#氧枪" or
                        key == "是否是全氧窑" or
                        key == "氧气介质密度" or
                        key == "氧气总管C计大" or
                        key == "氧气总管C计小" or
                        key == "氧气总管C选定" or
                        key == "氧气总管K大" or
                        key == "氧气总管K小" or
                        key == "main_valve_c_selected" or
                        key == "main_valve_k_max" or
                        key == "main_valve_k_min" or
                        key == "release_valve_dn" or
                        key == "release_valve_working_pressure" or  # 新增这一行
                        key == "release_valve_d0" or  # 新增这一行
                        key == "放散阀工作压力" or  # 新增这一行
                        key == "放散阀反算d0" or  # 新增这一行
                        key == "放散阀选取DN" or  # 新增这一行

                        key == "small_valve_data" or
                        key == "计算类型"):

                        # 如果是自力式阀数据，需要判断当前页面是否存在该阀
                        if key.startswith("自力式阀"):
                            # 如果当前界面有自力式阀数据
                            if hasattr(self, 'valve_tab_count'):
                                # 提取阀号
                                import re
                                match = re.match(r"自力式阀(\d+)", key)
                                if match:
                                    valve_num = int(match.group(1))
                                    # 检查该阀是否仍存在（没有被删除）
                                    if valve_num <= self.valve_tab_count:
                                        project_data[key] = value
                                else:
                                    project_data[key] = value
                            else:
                                # 如果当前界面没有自力式阀数据，保留所有自力式阀数据
                                project_data[key] = value
                        else:
                            project_data[key] = value



            # 【重要】添加0#氧枪数据到顶层
            if self.has_oxygen_lance.get() == "是":
                if hasattr(self, 'o2_flow'):
                    project_data["氧枪_氧气流量(Nm³/h)"] = self.o2_flow.get()
                if hasattr(self, 'o2_inlet_pressure'):
                    project_data["氧枪_氧气进车间压力(MPa)"] = self.o2_inlet_pressure.get()
                if hasattr(self, 'o2_main_valve_pre'):
                    project_data["氧枪_氧气主管阀前压力(MPa)"] = self.o2_main_valve_pre.get()
                if hasattr(self, 'o2_main_valve_post'):
                    project_data["氧枪_氧气主管阀后压力(MPa)"] = self.o2_main_valve_post.get()
                if hasattr(self, 'o2_branch_valve_pre'):
                    project_data["氧枪_氧气支管阀前压力(MPa)"] = self.o2_branch_valve_pre.get()
                if hasattr(self, 'o2_branch_valve_post'):
                    project_data["氧枪_氧气支管阀后压力(MPa)"] = self.o2_branch_valve_post.get()
                if hasattr(self, 'o2_density'):
                    project_data["氧枪_氧气密度(kg/m³)"] = self.o2_density.get()
                if hasattr(self, 'o2_velocity'):
                    project_data["氧枪_氧气流速(m/s)"] = self.o2_velocity.get()
                if hasattr(self, 'o2_relief_valve_pressure'):
                    project_data["氧枪_氧气放散阀开启压力"] = self.o2_relief_valve_pressure.get()

                if hasattr(self, 'o2_relief_valve_area'):
                    project_data["氧枪_氧气放散阀截面积A"] = self.o2_relief_valve_area.get()
                if hasattr(self, 'o2_main_calc_diameter'):
                    project_data["氧枪_氧气主管计算管径"] = self.o2_main_calc_diameter.get()
                if hasattr(self, 'o2_main_selected_diameter'):
                    project_data["氧枪_氧气选取主管管径"] = self.o2_main_selected_diameter.get()
                if hasattr(self, 'o2_main_actual_velocity'):
                    project_data["氧枪_氧气反算主管流速"] = self.o2_main_actual_velocity.get()
                if hasattr(self, 'o2_branch_pre_calc_diameter'):
                    project_data["氧枪_氧气支管计算阀前管径"] = self.o2_branch_pre_calc_diameter.get()
                if hasattr(self, 'o2_branch_pre_selected_diameter'):
                    project_data["氧枪_氧气选取支管阀前管径"] = self.o2_branch_pre_selected_diameter.get()
                if hasattr(self, 'o2_branch_pre_actual_velocity'):
                    project_data["氧枪_氧气反算支管阀前流速"] = self.o2_branch_pre_actual_velocity.get()
                if hasattr(self, 'o2_branch_post_calc_diameter'):
                    project_data["氧枪_氧气支管计算阀后管径"] = self.o2_branch_post_calc_diameter.get()
                if hasattr(self, 'o2_branch_post_selected_diameter'):
                    project_data["氧枪_氧气选取支管阀后管径"] = self.o2_branch_post_selected_diameter.get()
                if hasattr(self, 'o2_branch_post_actual_velocity'):
                    project_data["氧枪_氧气反算支管阀后流速"] = self.o2_branch_post_actual_velocity.get()

                if hasattr(self, 'o2_main_c_calculated'):
                    project_data["氧枪_氧气主管C计"] = self.o2_main_c_calculated.get()
                if hasattr(self, 'o2_main_c_selected'):
                    project_data["氧枪_氧气主管C选定"] = self.o2_main_c_selected.get()
                if hasattr(self, 'o2_main_k'):
                    project_data["氧枪_氧气主管K"] = self.o2_main_k.get()
                if hasattr(self, 'o2_branch_c_calculated'):
                    project_data["氧枪_氧气支管C计"] = self.o2_branch_c_calculated.get()
                if hasattr(self, 'o2_branch_c_selected'):
                    project_data["氧枪_氧气支管C选定"] = self.o2_branch_c_selected.get()
                if hasattr(self, 'o2_branch_k'):
                    project_data["氧枪_氧气支管K"] = self.o2_branch_k.get()

                # 天然气数据
                if hasattr(self, 'ng_flow'):
                    project_data["氧枪_天然气流量(Nm³/h)"] = self.ng_flow.get()
                if hasattr(self, 'ng_inlet_pressure'):
                    project_data["氧枪_天然气进车间压力(MPa)"] = self.ng_inlet_pressure.get()
                if hasattr(self, 'ng_main_valve_pre'):
                    project_data["氧枪_天然气主管阀前压力(MPa)"] = self.ng_main_valve_pre.get()
                if hasattr(self, 'ng_main_valve_post'):
                    project_data["氧枪_天然气主管阀后压力(MPa)"] = self.ng_main_valve_post.get()
                if hasattr(self, 'ng_branch_valve_pre'):
                    project_data["氧枪_天然气支管阀前压力(MPa)"] = self.ng_branch_valve_pre.get()
                if hasattr(self, 'ng_branch_valve_post'):
                    project_data["氧枪_天然气支管阀后压力(MPa)"] = self.ng_branch_valve_post.get()
                if hasattr(self, 'ng_density'):
                    project_data["氧枪_天然气密度(kg/m³)"] = self.ng_density.get()
                if hasattr(self, 'ng_velocity'):
                    project_data["氧枪_天然气流速(m/s)"] = self.ng_velocity.get()

                if hasattr(self, 'ng_main_calc_diameter'):
                    project_data["氧枪_天然气主管计算管径"] = self.ng_main_calc_diameter.get()
                if hasattr(self, 'ng_main_selected_diameter'):
                    project_data["氧枪_天然气选取主管管径"] = self.ng_main_selected_diameter.get()
                if hasattr(self, 'ng_main_actual_velocity'):
                    project_data["氧枪_天然气反算主管流速"] = self.ng_main_actual_velocity.get()
                if hasattr(self, 'ng_branch_pre_calc_diameter'):
                    project_data["氧枪_天然气支管计算阀前管径"] = self.ng_branch_pre_calc_diameter.get()
                if hasattr(self, 'ng_branch_pre_selected_diameter'):
                    project_data["氧枪_天然气选取支管阀前管径"] = self.ng_branch_pre_selected_diameter.get()
                if hasattr(self, 'ng_branch_pre_actual_velocity'):
                    project_data["氧枪_天然气反算支管阀前流速"] = self.ng_branch_pre_actual_velocity.get()
                if hasattr(self, 'ng_branch_post_calc_diameter'):
                    project_data["氧枪_天然气支管计算阀后管径"] = self.ng_branch_post_calc_diameter.get()
                if hasattr(self, 'ng_branch_post_selected_diameter'):
                    project_data["氧枪_天然气选取支管阀后管径"] = self.ng_branch_post_selected_diameter.get()
                if hasattr(self, 'ng_branch_post_actual_velocity'):
                    project_data["氧枪_天然气反算支管阀后流速"] = self.ng_branch_post_actual_velocity.get()

                if hasattr(self, 'ng_main_c_calculated'):
                    project_data["氧枪_天然气主管C计"] = self.ng_main_c_calculated.get()
                if hasattr(self, 'ng_main_c_selected'):
                    project_data["氧枪_天然气主管C选定"] = self.ng_main_c_selected.get()
                if hasattr(self, 'ng_main_k'):
                    project_data["氧枪_天然气主管K"] = self.ng_main_k.get()
                if hasattr(self, 'ng_branch_c_calculated'):
                    project_data["氧枪_天然气支管C计"] = self.ng_branch_c_calculated.get()
                if hasattr(self, 'ng_branch_c_selected'):
                    project_data["氧枪_天然气支管C选定"] = self.ng_branch_c_selected.get()
                if hasattr(self, 'ng_branch_k'):
                    project_data["氧枪_天然气支管K"] = self.ng_branch_k.get()

                # 如果没有其他计算类型，设置计算类型为0#氧枪计算
                if project_data.get("计算类型") != "压缩空气管道计算":
                    project_data["计算类型"] = "0#氧枪计算"

            for furnace in self.furnace_data:
                furnace_data = {
                    "平均热负荷": furnace['heat_load'].get(),
                    "浮动值": furnace['float_value'].get(),
                    "喷枪数": furnace['nozzle_count'].get(),
                    "选取阀前管径": furnace['selected_diameter'].get(),
                    "选取阀后管径": furnace['selected_post_diameter'].get(),
                    "计算阀后管径": furnace['calc_post_diameter'].cget("text"),
                    "反算阀后流速": furnace['calc_post_velocity'].cget("text")
                }
                project_data["小炉数据"].append(furnace_data)
            # 调试输出，检查最终要保存的数据
            print("保存的项目数据包含压缩空气支管字段:")
            for key in project_data.keys():
                if "支管" in key:
                    print(f"  - {key}: {project_data[key]}")


            # 读取现有历史记录
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = []


            # 检查是否存在相同工程名称和工程代号的记录
            found_index = -1
            name_exists = False
            code_exists = False
            for i, record in enumerate(history):
                if record.get("工程名称") == project_name:
                    name_exists = True
                    found_index = i
                    break
                if project_code and record.get("工程代号") == project_code:
                    code_exists = True
                    found_index = i
                    break

            # 如果找到匹配记录，则覆盖；否则添加新记录
            if found_index >= 0:
                history[found_index] = project_data
                action_msg = "项目已更新"
                if name_exists:
                    action_msg += "（工程名称相同，已覆盖原记录）"
                elif code_exists:
                    action_msg += "（工程代号相同，已覆盖原记录）"
            else:
                history.append(project_data)
                action_msg = "项目已保存"
            # 【重要】在最终写入history.json前，添加调试信息
            for i, record in enumerate(history):
                if record.get("工程名称") == project_data["工程名称"] and record.get("工程代号") == project_data["工程代号"]:
                    # 确保0#氧枪数据被保存
                    if self.has_oxygen_lance.get() == "是":
                        print(f"写入前检查氧枪数据: 氧气数据字段数:{len(project_data.get('氧枪氧气数据', {}))}, 天然气数据字段数:{len(project_data.get('氧枪天然气数据', {}))}")
                    break

            # 保存更新后的历史记录
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            # 更新历史记录显示
            self.update_history_display()

            if show_message:
                messagebox.showinfo("成功", "项目数据已保存")

        except Exception as e:
            messagebox.showerror("错误", f"保存项目时出错: {str(e)}")

    def create_side_menu(self):
        """创建侧边菜单栏，按照项目管理、计算工具、系统选项分类"""
        # 创建侧边栏容器框架
        side_menu = ttk.LabelFrame(self.main_container, text="功能导航")

        # 将侧边栏添加到主容器的最左侧
        self.main_container.insert(0, side_menu, weight=1)  # 侧边栏占1份宽度

        # 创建垂直布局来容纳菜单项
        menu_container = ttk.Frame(side_menu)
        menu_container.pack(fill="both", expand=True, padx=5, pady=5)

        # 1. 项目管理分组
        project_manage = ttk.LabelFrame(menu_container, text="项目管理")
        project_manage.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(project_manage, text="保存项目", command=self.save_project).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_manage, text="导入项目json", command=self.import_project_json).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(project_manage, text="导出项目json", command=self.export_project_json).pack(fill="x", padx=5, pady=2)
    
        # 添加设备表按钮
        ttk.Button(project_manage, text="设备表", command=self.equipment_manager.show_equipment_list).pack(fill=tk.X, padx=5, pady=3)

        # 2. 计算工具分组
        calc_tools = ttk.LabelFrame(menu_container, text="计算工具")
        calc_tools.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(calc_tools, text="刷新计算", command=self.refresh_all_calculations).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(calc_tools, text="压缩空气计算", command=self.show_compressed_air_calculator).pack(fill=tk.X, padx=5, pady=3)

        ttk.Button(calc_tools, text="调节阀计算", command=self.show_valve_calculator).pack(fill=tk.X, padx=5, pady=3)
         # 添加自力式阀计算按钮
        ttk.Button(calc_tools, text="自力式阀计算", command=self.show_self_operated_valve_calculator).pack(fill=tk.X, padx=5, pady=3)

        # 创建0#氧枪和全氧窑氧气管道计算按钮，但初始状态设为禁用
        self.oxygen_lance_button = ttk.Button(calc_tools, text="0#氧枪计算", command=self.show_oxygen_lance_calculator, state="disabled")
        self.oxygen_lance_button.pack(fill=tk.X, padx=5, pady=3)

        self.oxygen_pipe_button = ttk.Button(calc_tools, text="全氧窑氧气管道计算", command=self.show_oxygen_pipe_calculator, state="disabled")
        self.oxygen_pipe_button.pack(fill=tk.X, padx=5, pady=3)
        # 3. 系统选项分组
        system_options = ttk.LabelFrame(menu_container, text="系统选项")
        system_options.pack(fill=tk.X, padx=5, pady=5)
        ttk.Button(system_options, text="设置", command=self.show_settings_window).pack(fill=tk.X, padx=5, pady=3)
        ttk.Button(system_options, text="历史记录管理", command=self.show_history_manager).pack(fill=tk.X, padx=5, pady=3)
    # 1. 修复导出功能，确保直接导出JSON数据
    # 1. 添加专门的JSON导入函数
    def import_project_json(self):
        """导入项目JSON数据文件"""
        try:
            # 打开文件选择对话框，限制为JSON文件
            file_path = filedialog.askopenfilename(
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                title="选择项目JSON文件",
                initialdir=os.path.expanduser("~")
            )
            
            if not file_path:
                print("用户取消了导入操作")
                return
            
            # 检查文件扩展名
            if not file_path.lower().endswith('.json'):
                messagebox.showwarning("格式错误", "请选择JSON格式文件(.json)")
                return
                
            print(f"正在导入JSON文件: {file_path}")
            
            # 读取JSON文件
            with open(file_path, 'r', encoding='utf-8') as f:
                project_data = json.load(f)
            
            # 检查是否包含基本项目信息
            if "工程名称" not in project_data and "工程代号" not in project_data:
                messagebox.showwarning("格式错误", "所选文件不包含有效的项目数据")
                return
            
            # 使用load_project方法代替safe_fill_data_from_dict方法
            self.load_project(project_data)
            
            # 保存到历史记录
            self.save_project(show_message=False)
            
            messagebox.showinfo("导入成功", f"已成功导入项目: {project_data.get('工程名称', '')} {project_data.get('工程代号', '')}")
            
        except json.JSONDecodeError:
            messagebox.showerror("格式错误", "所选文件不是有效的JSON格式")
        except Exception as e:
            messagebox.showerror("导入失败", f"导入项目JSON数据时出错:\n{str(e)}")
            traceback.print_exc()
    # 2. 添加新方法来处理正常生产时流量变化
    def update_equipment_normal_flow(self, *args):
        """当normal_flow变化时，确保设备表更新"""
        print("normal_flow变化, 正在更新设备表...")
        
        # 确保equipment_manager已初始化
        if hasattr(self, 'equipment_manager'):
            # 直接调用设备表的更新方法
            if hasattr(self.equipment_manager, 'update_valve_flow_rate'):
                try:
                    self.equipment_manager.update_valve_flow_rate()
                    print("设备表流量更新完成")
                except Exception as e:
                    print(f"更新设备表流量时出错: {str(e)}")

    # 2. 修复导出项目json文件的功能，确保是.json而不是Excel
    def export_project_json(self):
        """导出当前项目数据为json文件"""
        try:
            # 获取当前项目名称和代号
            project_name = self.project_name.get().strip()
            project_code = self.project_code.get().strip()
            
            if not project_name and not project_code:
                messagebox.showwarning("警告", "请先输入项目名称或项目代号")
                return
            
            # 生成文件名
            file_name = f"{project_name}_{project_code}" if project_name and project_code else project_name or project_code
            file_name = file_name.replace(" ", "_").replace("/", "_").replace("\\", "_")
            
            # 选择保存路径 - 明确指定json文件类型
            save_path = filedialog.asksaveasfilename(
                initialfile=f"{file_name}.json",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json")],
                title="保存项目JSON数据"
            )
            
            if not save_path:
                print("用户取消了导出操作")
                return  # 用户取消了保存
                
            # 确保文件扩展名是.json
            if not save_path.lower().endswith('.json'):
                save_path += '.json'
            
            # 保存当前项目状态
            self.save_project(show_message=False)
                # 收集当前项目数据
            project_data = {
                "工程名称": project_name,
                "工程代号": project_code,
                "导出时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
            # 从历史文件中读取当前项目的完整数据
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                        for record in history:
                            if record.get("工程名称") == project_name and record.get("工程代号") == project_code:
                                # 合并所有数据
                                project_data = record.copy()
                                print(f"从历史记录中找到项目数据: {project_name}_{project_code}")
                                break
                except Exception as e:
                    print(f"读取历史数据出错: {str(e)}")
                    traceback.print_exc()
            
            # 确保JSON格式正确
            for key in list(project_data.keys()):
                # 处理不能序列化的对象
                if isinstance(project_data[key], (tk.StringVar, tk.IntVar, tk.DoubleVar, tk.BooleanVar)):
                    project_data[key] = project_data[key].get()
            
            # 直接保存为JSON文件
            print(f"正在保存JSON数据到: {save_path}")
            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("导出成功", f"项目JSON数据已保存到:\n{save_path}")
            
        except Exception as e:
            messagebox.showerror("导出失败", f"导出项目数据时出错:\n{str(e)}")
            traceback.print_exc()
    def safe_fill_data_from_dict(self, data_dict):
        """安全版本的数据填充，处理各种错误情况"""
        try:
            # 清空当前数据
            self.clear_form_data()
            
            # 填充基本项目信息
            if "工程名称" in data_dict:
                self.project_name.set(data_dict["工程名称"])
            if "工程代号" in data_dict:
                self.project_code.set(data_dict["工程代号"])
            if "项目类型" in data_dict:
                self.project_type.set(data_dict["项目类型"])
            if "项目地址" in data_dict:
                self.project_location.set(data_dict["项目地址"])
            if "产能(t/d)" in data_dict:
                self.capacity.set(data_dict["产能(t/d)"])
            if "小炉数" in data_dict:
                self.furnace_count.set(data_dict["小炉数"])
            if "一窑几线" in data_dict:
                self.line_count.set(data_dict["一窑几线"])
            
            # 流量数据
            if "安全阀流量(Nm³/h)" in data_dict:
                self.safety_valve_flow.set(data_dict["安全阀流量(Nm³/h)"])
            if "正常生产流量(Nm³/h)" in data_dict:
                self.normal_flow.set(data_dict["正常生产流量(Nm³/h)"])
            if "交通路点流量(Nm³/h)" in data_dict:
                self.crossing_flow.set(data_dict["交通路点流量(Nm³/h)"])
            if "成型窑加热流量(Nm³/h)" in data_dict:
                self.forming_flow.set(data_dict["成型窑加热流量(Nm³/h)"])
            if "边火加热流量(Nm³/h)" in data_dict:
                self.edge_heating_flow.set(data_dict["边火加热流量(Nm³/h)"])
            # 压力数据
            if "进车间压力(MPa)" in data_dict:
                self.inlet_pressure.set(data_dict["进车间压力(MPa)"])
            if "总管调节阀前压力(MPa)" in data_dict:
                self.main_pre_pressure.set(data_dict["总管调节阀前压力(MPa)"])
            if "总管调节阀后压力(MPa)" in data_dict:
                self.main_post_pressure.set(data_dict["总管调节阀后压力(MPa)"])
            if "小炉支管调节阀前压力(MPa)" in data_dict:
                self.branch_pre_pressure.set(data_dict["小炉支管调节阀前压力(MPa)"])
            if "小炉支管调节阀后压力(MPa)" in data_dict:
                self.branch_post_pressure.set(data_dict["小炉支管调节阀后压力(MPa)"])
            if "支通路总管阀前压力(MPa)" in data_dict:
                self.bypass_main_pre_pressure.set(data_dict["支通路总管阀前压力(MPa)"])
            if "支通路总管阀后压力(MPa)" in data_dict:
                self.bypass_main_post_pressure.set(data_dict["支通路总管阀后压力(MPa)"])
            
            # 管径数据
            if "总管阀前计算管径(mm)" in data_dict:
                self.main_pre_diameter.set(data_dict["总管阀前计算管径(mm)"])
            if "总管阀前选定管径(mm)" in data_dict:
                self.main_pre_selected_diameter.set(data_dict["总管阀前选定管径(mm)"])
            if "总管阀后计算管径(mm)" in data_dict:
                self.main_post_diameter.set(data_dict["总管阀后计算管径(mm)"])
            if "总管阀后选定管径(mm)" in data_dict:
                self.main_post_selected_diameter.set(data_dict["总管阀后选定管径(mm)"])
            
            # 流速和温度数据
            if "天然气总管流速(m/s)" in data_dict:
                self.main_velocity.set(data_dict["天然气总管流速(m/s)"])
            if "小炉支管流速(m/s)" in data_dict:
                self.branch_velocity.set(data_dict["小炉支管流速(m/s)"])
            if "设计温度(℃)" in data_dict:
                self.design_temperature.set(data_dict["设计温度(℃)"])
             # 填充压缩空气数据到内存，供后续使用
            try:
                if hasattr(self, 'compressed_air_calculator'):
                    air_data = {}
                    for key, value in data_dict.items():
                        if (key.startswith("压缩空气") or 
                            key in ["进车间压力", "设计流速", "连续用气总量", "间歇用气总量", 
                                    "总用气量", "计算管径", "选取管径", "实际流速"] or
                            key.startswith("蓄热室吹扫用气") or key.startswith("喷枪冷却用气") or
                            key.startswith("投料机用气") or key.startswith("阀及工业电视用气") or
                            key.startswith("冷端机组用气") or key.startswith("退火窑及红外用气") or
                            key.startswith("支通路加热用气") or key.startswith("压延机烧边火用气") or
                            key.startswith("压延机清理用气")):
                            air_data[key] = value
                    
                    # 如果有压缩空气数据，保存到内存中
                    if air_data:
                        if not hasattr(self, 'saved_air_data'):
                            self.saved_air_data = {}
                        
                        project_id = f"{self.project_name.get()}_{self.project_code.get()}"
                        self.saved_air_data[project_id] = air_data
                        print(f"已保存压缩空气数据到内存，项目ID: {project_id}")
            except Exception as e:
                print(f"加载压缩空气数据时出错: {str(e)}")
            # 安全处理氧枪数据
            try:
                # 氧气窑相关数据
                if "是否是全氧窑" in data_dict and hasattr(self, 'is_oxygen_kiln'):
                    self.is_oxygen_kiln.set(data_dict["是否是全氧窑"])
                if "是否有0#氧枪" in data_dict and hasattr(self, 'has_oxygen_lance'):
                    self.has_oxygen_lance.set(data_dict["是否有0#氧枪"])
                    
                # 氧枪数据 - 只在类有相应方法时才调用
                if hasattr(self, 'oxygen_lance_calculator'):
                    if "氧枪氧气数据" in data_dict and hasattr(self.oxygen_lance_calculator, 'load_oxygen_data'):
                        try:
                            self.oxygen_lance_calculator.load_oxygen_data(data_dict["氧枪氧气数据"])
                            print("已加载氧枪氧气数据")
                        except Exception as e:
                            print(f"加载氧枪氧气数据时出错: {str(e)}")
                    
                    if "氧枪天然气数据" in data_dict and hasattr(self.oxygen_lance_calculator, 'load_natural_gas_data'):
                        try:
                            self.oxygen_lance_calculator.load_natural_gas_data(data_dict["氧枪天然气数据"])
                            print("已加载氧枪天然气数据")
                        except Exception as e:
                            print(f"加载氧枪天然气数据时出错: {str(e)}")
                    
                    # 如果没有对应的方法，则手动加载氧枪数据到对象变量中
                    if "氧枪氧气数据" in data_dict and not hasattr(self.oxygen_lance_calculator, 'load_oxygen_data'):
                        print("氧枪计算器缺少load_oxygen_data方法，尝试直接设置数据")
                        setattr(self.oxygen_lance_calculator, 'oxygen_data', data_dict["氧枪氧气数据"])
                    
                    if "氧枪天然气数据" in data_dict and not hasattr(self.oxygen_lance_calculator, 'load_natural_gas_data'):
                        print("氧枪计算器缺少load_natural_gas_data方法，尝试直接设置数据")
                        setattr(self.oxygen_lance_calculator, 'natural_gas_data', data_dict["氧枪天然气数据"])
            except Exception as e:
                print(f"处理氧枪数据时出错: {str(e)}")
            # 重新计算所有值
            try:
                self.refresh_all_calculations()
            except Exception as e:
                print(f"刷新计算时出错: {str(e)}")
                traceback.print_exc()
            
            # 刷新显示
            self.update()
            print("数据填充完成")
        except Exception as e:
            print(f"填充数据时出错: {str(e)}")
            traceback.print_exc()
            # 重要：确保即使出错也能返回而不会导致整个导入过程失败
            return False
        
        return True
            
    def show_equipment_list(self):
        self.equipment_manager.show_equipment_list()


    def update_oxygen_tools_state(self):
        """根据氧枪和全氧窑选项更新计算工具按钮状态"""
        # 根据是否有0#氧枪更新按钮状态
        if self.has_oxygen_lance.get() == "是":
            self.oxygen_lance_button.configure(state="normal")
        else:
            self.oxygen_lance_button.configure(state="disabled")

        # 根据是否是全氧窑更新按钮状态
        if self.is_oxygen_kiln.get() == "是":
            self.oxygen_pipe_button.configure(state="normal")
        else:
            self.oxygen_pipe_button.configure(state="disabled")
    def show_history_manager(self):
        """显示历史记录管理窗口，提供更丰富的历史记录管理功能"""
        # 如果窗口已经打开，则聚焦到该窗口
        if 'history_window' in self.open_windows and self.open_windows['history_window'].winfo_exists():
            self.open_windows['history_window'].focus_set()
            return
        # 创建顶层窗口
        history_window = tk.Toplevel(self.root)
        history_window.title("历史记录管理")
        history_window.geometry("900x600")  # 增加窗口宽度以容纳所有搜索框
        #history_window.transient(self.root)
        #history_window.grab_set()
        # 【新增】将窗口添加到管理系统
        self.open_windows['history_window'] = history_window
        # 设置窗口图标
        if hasattr(self, 'icon_path') and os.path.exists(self.icon_path):
            history_window.iconbitmap(self.icon_path)

        # 创建主框架
        main_frame = ttk.Frame(history_window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        # 初始化历史记录变量
        history = []

        # 创建搜索区域
        search_frame = ttk.LabelFrame(main_frame, text="搜索条件")
        search_frame.pack(fill="x", padx=5, pady=5)

        # 创建搜索条件输入控件 - 第一行
        ttk.Label(search_frame, text="工程名称:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
        name_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=name_var, width=15).grid(row=0, column=1, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="工程代号:").grid(row=0, column=2, padx=5, pady=5, sticky="w")
        code_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=code_var, width=15).grid(row=0, column=3, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="项目类型:").grid(row=0, column=4, padx=5, pady=5, sticky="w")
        type_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=type_var, width=15).grid(row=0, column=5, padx=5, pady=5, sticky="w")
        # 添加吨位搜索
        ttk.Label(search_frame, text="吨位:").grid(row=0, column=6, padx=5, pady=5, sticky="w")
        capacity_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=capacity_var, width=15).grid(row=0, column=7, padx=5, pady=5, sticky="w")

        # 第二行 - 日期范围和小炉数
        ttk.Label(search_frame, text="日期范围:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
        date_from_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=date_from_var, width=15).grid(row=1, column=1, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="至").grid(row=1, column=2, padx=5, pady=5, sticky="w")
        date_to_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=date_to_var, width=15).grid(row=1, column=3, padx=5, pady=5, sticky="w")


        # 将小炉数和一窑几线放在最右侧
        ttk.Label(search_frame, text="小炉数:").grid(row=1, column=4, padx=5, pady=5, sticky="w")
        furnace_count_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=furnace_count_var, width=15).grid(row=1, column=5, padx=5, pady=5, sticky="w")

        ttk.Label(search_frame, text="一窑几线:").grid(row=1, column=6, padx=5, pady=5, sticky="w")
        line_count_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=line_count_var, width=15).grid(row=1, column=7, padx=5, pady=5, sticky="w")


       # 将按钮放在第三行
        btn_frame = ttk.Frame(search_frame)
        btn_frame.grid(row=2, column=5, columnspan=3, padx=5, pady=5, sticky="e")

        ttk.Button(btn_frame, text="搜索", command=lambda: search_records()).pack(side="right", padx=5)
        ttk.Button(btn_frame, text="重置", command=lambda: [
            name_var.set(""),
            code_var.set(""),
            type_var.set(""),
            date_from_var.set(""),
            date_to_var.set(""),
            furnace_count_var.set(""),
            line_count_var.set(""),
            capacity_var.set(""),
            load_all_records()
        ]).pack(side="right", padx=5)



        # 创建结果显示区域
        result_frame = ttk.LabelFrame(main_frame, text="搜索结果")
        result_frame.pack(fill="both", expand=True, padx=5, pady=5)

        # 创建树形视图以显示结果 - 移除计算类型列
        columns = ("时间", "工程名称", "工程代号", "项目类型",  "吨位", "小炉数", "一窑几线")
        history_tree = ttk.Treeview(result_frame, columns=columns, show="headings")

        # 设置列标题和宽度，确保均匀分布
        total_width = result_frame.winfo_width() or 780  # 如果winfo_width返回0，使用默认值
        column_width = total_width // len(columns)

        for col in columns:
            history_tree.heading(col, text=col)
            history_tree.column(col, width=column_width, anchor="center")  # 居中显示内容

        # 添加滚动条
        scroll_y = ttk.Scrollbar(result_frame, orient="vertical", command=history_tree.yview)
        scroll_x = ttk.Scrollbar(result_frame, orient="horizontal", command=history_tree.xview)
        history_tree.configure(yscrollcommand=scroll_y.set, xscrollcommand=scroll_x.set)

        scroll_y.pack(side="right", fill="y")
        scroll_x.pack(side="bottom", fill="x")
        history_tree.pack(side="left", fill="both", expand=True)

        # 添加状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.pack(fill="x", padx=5, pady=5)

        status_var = tk.StringVar(value="准备就绪")
        ttk.Label(status_frame, textvariable=status_var).pack(side="left")

        # 添加操作按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        def search_records():
            """执行高级搜索功能"""
            # 从历史文件中读取记录
            history = []
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception as e:
                    messagebox.showerror("错误", f"读取历史记录时出错: {str(e)}")
                    return

            # 过滤记录
            filtered = []
            for record in history:
                # 检查各个搜索条件
                name_match = True
                if name_var.get():
                    name_match = name_var.get().lower() in str(record.get("工程名称", "")).lower()

                code_match = True
                if code_var.get():
                    code_match = code_var.get().lower() in str(record.get("工程代号", "")).lower()

                type_match = True
                if type_var.get():
                    type_match = type_var.get().lower() in str(record.get("项目类型", "")).lower()
                # 添加吨位搜索条件
                capacity_match = True
                if capacity_var.get():
                    capacity_match = capacity_var.get() in str(record.get("吨位", ""))


                # 小炉数搜索条件
                furnace_count_match = True
                if furnace_count_var.get():
                    # 将记录中的小炉数转为字符串进行包含比较
                    furnace_count_match = furnace_count_var.get() in str(record.get("小炉数", ""))

                # 一窑几线搜索条件
                line_count_match = True
                if line_count_var.get():
                    # 将记录中的一窑几线转为字符串进行包含比较
                    line_count_match = line_count_var.get() in str(record.get("一窑几线", ""))

                date_match = True
                if date_from_var.get() or date_to_var.get():
                    try:
                        record_date = datetime.strptime(record.get("时间", ""), "%Y-%m-%d %H:%M:%S")

                        if date_from_var.get():
                            from_date = datetime.strptime(date_from_var.get(), "%Y-%m-%d")
                            if record_date < from_date:
                                date_match = False

                        if date_to_var.get():
                            to_date = datetime.strptime(date_to_var.get(), "%Y-%m-%d")
                            if record_date > to_date:
                                date_match = False
                    except:
                        # 日期格式错误，忽略此条件
                        pass

                # 所有条件都匹配才添加到结果中
                if (name_match and code_match and type_match and date_match and
                    furnace_count_match and line_count_match and capacity_match):
                    filtered.append(record)

            # 更新显示
            history_tree.delete(*history_tree.get_children())
            for record in filtered:
                values = (
                    record.get("时间", ""),
                    record.get("工程名称", ""),
                    record.get("工程代号", ""),
                    record.get("项目类型", ""),
                    record.get("吨位", ""),
                    record.get("小炉数", ""),
                    record.get("一窑几线", ""),

                )
                history_tree.insert("", "end", values=values)

            # 更新统计信息
            status_var.set(f"共找到 {len(filtered)} 条匹配记录")

        # 添加按钮操作
        ttk.Button(button_frame, text="加载选中项目", command=lambda: load_selected_record()).pack(side="left", padx=5)
        ttk.Button(button_frame, text="导出选中记录", command=lambda: export_selected_records()).pack(side="left", padx=5)
        ttk.Button(button_frame, text="删除选中记录", command=lambda: delete_selected_records()).pack(side="left", padx=5)
        # 添加编辑按钮（如果还没有）
        ttk.Button(button_frame, text="编辑选中记录", command=lambda: edit_selected_record()).pack(side="left", padx=5)

        ttk.Button(button_frame, text="关闭", command=history_window.destroy).pack(side="right", padx=5)
        # 在窗口打开时加载所有记录
        def load_all_records():
            nonlocal history
            history = []
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)

                    # 更新显示 - 不显示计算类型
                    for record in history:
                        values = (
                            record.get("时间", ""),
                            record.get("工程名称", ""),
                            record.get("工程代号", ""),
                            record.get("项目类型", ""),
                            record.get("吨位", ""),  # 添加吨位显示
                            record.get("小炉数", ""),
                            record.get("一窑几线", "")
                        )
                        history_tree.insert("", "end", values=values)

                    status_var.set(f"共加载 {len(history)} 条记录")
                except Exception as e:
                    messagebox.showerror("错误", f"读取历史记录时出错: {str(e)}")
                    status_var.set("加载失败")
            else:
                status_var.set("没有找到历史记录文件")

        def load_selected_record():
            """加载选中的历史记录"""
            try:
                # 检查窗口和树形视图是否仍然存在
                if not history_window.winfo_exists():
                    return
                
                selected = history_tree.selection()
                if not selected:
                    messagebox.showinfo("提示", "请先选择一条记录")
                    return

                # 获取选中记录的数据
                values = history_tree.item(selected[0], "values")
                if not values or len(values) < 2:
                    return

                # 存储匹配的记录，确保在关闭窗口前找到对应记录
                target_record = None
                for record in history:
                    if (record.get("时间") == values[0] and
                        record.get("工程名称") == values[1] and
                        record.get("工程代号") == values[2]):
                        target_record = record
                        break
                
                if not target_record:
                    messagebox.showinfo("提示", "未找到匹配的历史记录")
                    return
                    
                # 先从管理系统中移除窗口
                if 'history_window' in self.open_windows:
                    del self.open_windows['history_window']
                    
                # 关闭窗口
                history_window.destroy()
                
                # 窗口关闭后再加载项目
                try:
                    self.load_project(target_record)
                    messagebox.showinfo("成功", "已加载选中项目")
                except Exception as e:
                    print(f"加载项目时出错: {str(e)}")
                    messagebox.showerror("错误", f"加载项目时出错: {str(e)}")
                    
            except Exception as e:
                print(f"加载选中记录时出错: {str(e)}")
                # 确保窗口存在再尝试关闭
                if 'history_window' in self.open_windows and self.open_windows['history_window'].winfo_exists():
                    self.open_windows['history_window'].destroy()

        def export_selected_records():
            """导出选中的历史记录"""
            selected = history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择一条或多条记录")
                return

            # 选择导出文件
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")],
                title="导出历史记录"
            )

            if not file_path:
                return

            # 收集选中的记录
            export_records = []
            for item in selected:
                values = history_tree.item(item, "values")
                if not values or len(values) < 2:
                    continue

                # 从历史数据中查找匹配记录
                for record in history:
                    if (record.get("时间") == values[0] and
                        record.get("工程名称") == values[1] and
                        record.get("工程代号") == values[2]):
                        export_records.append(record)
                        break

            if not export_records:
                messagebox.showinfo("提示", "未找到匹配的历史记录")
                return

            # 导出到文件
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_records, f, ensure_ascii=False, indent=2)

                messagebox.showinfo("成功", f"已导出 {len(export_records)} 条记录到 {file_path}")
            except Exception as e:
                messagebox.showerror("错误", f"导出历史记录时出错: {str(e)}")

        def delete_selected_records():
            """删除选中的历史记录"""
            selected = history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择一条或多条记录")
                return

            if not messagebox.askyesno("确认", "确定要删除选中的记录吗？此操作不可撤销。"):
                return

            # 收集选中的记录
            to_delete = []
            for item in selected:
                values = history_tree.item(item, "values")
                if not values or len(values) < 2:
                    continue

                # 从历史数据中查找匹配记录
                for i, record in enumerate(history):
                    if (record.get("时间") == values[0] and
                        record.get("工程名称") == values[1] and
                        record.get("工程代号") == values[2]):
                        to_delete.append(i)
                        break

            if not to_delete:
                messagebox.showinfo("提示", "未找到匹配的历史记录")
                return

            # 从后往前删除，避免索引变化
            to_delete.sort(reverse=True)
            for idx in to_delete:
                if idx < len(history):
                    del history[idx]

            # 保存修改后的历史记录
            try:
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump(history, f, ensure_ascii=False, indent=2)

                # 更新显示
                search_records()

                messagebox.showinfo("成功", f"已删除 {len(to_delete)} 条记录")

                # 同时更新主界面的历史记录显示
                self.update_history_display()
            except Exception as e:
                messagebox.showerror("错误", f"删除历史记录时出错: {str(e)}")
        # 添加编辑记录的函数
        def edit_selected_record():
            """编辑选中的历史记录"""
            try:
                selected = history_tree.selection()
                if not selected:
                    messagebox.showinfo("提示", "请先选择一条记录")
                    return

                # 获取选中记录的数据
                values = history_tree.item(selected[0], "values")
                if not values or len(values) < 2:
                    return

                # 从历史数据中查找匹配记录
                for i, record in enumerate(history):
                    if (record.get("时间") == values[0] and
                        record.get("工程名称") == values[1] and
                        record.get("工程代号") == values[2]):

                        # 创建编辑窗口
                        edit_window = tk.Toplevel(history_window)
                        edit_window.title("编辑历史记录")
                        edit_window.geometry("400x300")
                        edit_window.resizable(False, False)
                        # 创建编辑表单
                        ttk.Label(edit_window, text="工程名称:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
                        project_name_var = tk.StringVar(value=record.get("工程名称", ""))
                        ttk.Entry(edit_window, textvariable=project_name_var, width=30).grid(row=0, column=1, padx=5, pady=5, sticky="w")

                        ttk.Label(edit_window, text="工程代号:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
                        project_code_var = tk.StringVar(value=record.get("工程代号", ""))
                        ttk.Entry(edit_window, textvariable=project_code_var, width=30).grid(row=1, column=1, padx=5, pady=5, sticky="w")

                        ttk.Label(edit_window, text="项目类型:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
                        project_type_var = tk.StringVar(value=record.get("项目类型", ""))
                        ttk.Entry(edit_window, textvariable=project_type_var, width=30).grid(row=2, column=1, padx=5, pady=5, sticky="w")

                        # 保存按钮
                        def save_edit():
                            # 更新记录
                            record["工程名称"] = project_name_var.get()
                            record["工程代号"] = project_code_var.get()
                            record["项目类型"] = project_type_var.get()

                            # 保存更新后的历史记录
                            try:
                                with open(self.history_file, 'w', encoding='utf-8') as f:
                                    json.dump(history, f, ensure_ascii=False, indent=2)

                                # 更新显示
                                search_records()
                                messagebox.showinfo("成功", "记录已更新")
                                edit_window.destroy()
                            except Exception as e:
                                messagebox.showerror("错误", f"保存更新时出错: {str(e)}")

                        ttk.Button(edit_window, text="保存", command=save_edit).grid(row=3, column=0, columnspan=2, padx=5, pady=10)
                        # 设置窗口居中
                        self.center_window(edit_window)
                        return

                messagebox.showinfo("提示", "未找到匹配的历史记录")
            except Exception as e:
                messagebox.showerror("错误", f"编辑记录时出错: {str(e)}")



        # 添加窗口关闭事件处理
        def on_history_window_close():
            try:
                # 先清理treeview上的绑定
                history_tree.unbind_all()
                
                # 从窗口管理系统中移除
                if 'history_window' in self.open_windows:
                    del self.open_windows['history_window']
                    
                # 最后关闭窗口
                history_window.destroy()
            except Exception as e:
                print(f"关闭历史记录窗口时出错: {str(e)}")
                history_window.destroy()
        # 将关闭按钮的命令改为
        ttk.Button(button_frame, text="关闭", command=on_history_window_close).pack(side="right", padx=5)
        # 绑定窗口关闭事件
        history_window.protocol("WM_DELETE_WINDOW", on_history_window_close)
        # 加载所有记录
        history_window.after(100, load_all_records)

        # 设置窗口居中
        self.center_window(history_window)
    # 添加关闭所有窗口的方法
    def close_all_windows(self):
        """关闭所有打开的窗口"""
        windows_to_close = list(self.open_windows.keys())
        for window_name in windows_to_close:
            if window_name in self.open_windows and self.open_windows[window_name].winfo_exists():
                try:
                    self.open_windows[window_name].destroy()
                except Exception as e:
                    print(f"关闭窗口 {window_name} 时出错: {str(e)}")

        # 清空窗口列表
        self.open_windows.clear()
    # 添加一个方法用于在主窗口关闭时关闭所有子窗口
    def on_closing(self):
        """主窗口关闭事件处理"""
        try:

            # 确保压缩空气数据被保存
            if hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator:
                try:
                    # 调用save_air_data方法，按项目ID存储压缩空气数据
                    self.save_air_data()
                    print("主窗口关闭时保存了压缩空气数据")
                except Exception as e:
                    print(f"关闭窗口时保存压缩空气数据出错: {str(e)}")
                    traceback.print_exc()
            # 确保小炉阀门C选定值被保存
            if hasattr(self, 'small_valve_persistent_data') and len(self.small_valve_persistent_data) > 0:
                try:
                    # 读取历史文件获取当前项目记录
                    if os.path.exists(self.history_file):
                        with open(self.history_file, 'r', encoding='utf-8') as f:
                            history = json.load(f)

                        # 查找当前项目
                        current_project_name = self.project_name.get()
                        current_project_code = self.project_code.get()

                        for record in history:
                            if record.get("工程名称") == current_project_name and record.get("工程代号") == current_project_code:
                                # 更新小炉阀门C选定值
                                for i, valve_data in enumerate(self.small_valve_persistent_data):
                                    if i < self.furnace_count.get():
                                        key = f"小炉{i+1}调节阀C选定"
                                        value = valve_data.get('c_selected', '')
                                        record[key] = value
                                        print(f"主窗口关闭时保存了小炉{i+1}的C选定值: {value}")

                                # 写回历史文件
                                with open(self.history_file, 'w', encoding='utf-8') as f:
                                    json.dump(history, f, ensure_ascii=False, indent=2)

                                print("主窗口关闭时更新了历史文件中的小炉C选定值")
                                break
                except Exception as e:
                    print(f"关闭窗口时保存小炉阀门C选定值出错: {str(e)}")
                    traceback.print_exc()
            # 保存当前项目数据到历史文件
            self.save_project(show_message=False)

            # 关闭所有子窗口
            self.close_all_windows()

            # 保存窗口配置
            self.save_config()

            # 退出程序
            self.root.destroy()
        except Exception as e:
            print(f"关闭主窗口时出错: {str(e)}")
            self.root.destroy()

    # 添加focus_tab方法，用于切换到指定标签
    def focus_tab(self, tab_name):
        """切换到指定的标签页"""
        if tab_name == "项目信息栏":
            # 聚焦到项目信息框架
            self.project_info_frame.focus_set()

    # 添加focus_history方法，用于切换到历史记录区
    def focus_history(self):
        """切换到历史记录区"""
        self.history_frame.focus_set()

     # 添加以下三个方法实现
    def show_compressed_air_calculator(self):
        """显示压缩空气管道计算窗口"""
# 如果压缩空气窗口已经打开，先保存当前数据到内存，然后关闭窗口
        if hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator and \
        hasattr(self.compressed_air_calculator, 'air_window') and \
        self.compressed_air_calculator.air_window and \
        self.compressed_air_calculator.air_window.winfo_exists():
            # 先保存当前数据
            try:
                self.save_air_data()
                print("已保存现有压缩空气窗口数据")
            except Exception as e:
                print(f"保存现有压缩空气数据时出错: {str(e)}")
            
            # 关闭窗口
            try:
                # 先触发窗口关闭事件处理函数，确保数据保存到history.json
                self.compressed_air_calculator.on_window_close()
                self.compressed_air_calculator.air_window.destroy()
                print("已关闭现有压缩空气窗口")
            except Exception as e:
                print(f"关闭压缩空气窗口时出错: {str(e)}")
        
        # 重新创建压缩空气计算器实例
        self.compressed_air_calculator = CompressedAirCalculator(self)
        print("已重新创建压缩空气计算器实例")
        
        # 调用显示窗口方法
        self.compressed_air_calculator.show_window()
    def show_oxygen_lance_calculator(self):
        """显示0#氧枪计算窗口"""
        # 调用氧枪计算器的显示方法
        self.oxygen_lance_calculator.show_calculator()


    def show_oxygen_pipe_calculator(self):
        """显示全氧窑氧气管道计算窗口"""


        # 首次调用时创建计算器对象
        if self.oxygen_pipe_calculator is None:
            self.oxygen_pipe_calculator = OxygenPipeCalculator(self.root, self)

        # 调用计算器对象的显示方法
        self.oxygen_pipe_calculator.show_window()

    def show_settings_window(self):
        """显示设置窗口"""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("设置")
        settings_window.geometry("400x500")
        settings_window.resizable(False, False)

        # 创建选项卡控件
        notebook = ttk.Notebook(settings_window)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # 创建窗口大小设置选项卡
        window_size_frame = ttk.Frame(notebook)
        notebook.add(window_size_frame, text="窗口大小")

        # 创建主题设置选项卡
        theme_frame = ttk.Frame(notebook)
        notebook.add(theme_frame, text="主题设置")

        # 创建其他设置选项卡
        other_frame = ttk.Frame(notebook)
        notebook.add(other_frame, text="其他设置")

        # 窗口大小设置的内容
        self.create_window_size_settings(window_size_frame)

        # 主题设置的内容
        self.create_theme_settings(theme_frame)

        # 其他设置的内容
        self.create_other_settings(other_frame)

        # 底部按钮
        button_frame = ttk.Frame(settings_window)
        button_frame.pack(fill="x", padx=10, pady=10)
        ttk.Button(button_frame, text="关闭", command=settings_window.destroy).pack(side="right", padx=5)

        # 在窗口创建完成后，将其居中显示在主窗口上
        settings_window.update_idletasks()

        # 获取对话框的实际大小
        dialog_width = settings_window.winfo_width()
        dialog_height = settings_window.winfo_height()

        # 获取主窗口的位置和大小
        main_x = self.root.winfo_x()
        main_y = self.root.winfo_y()
        main_width = self.root.winfo_width()
        main_height = self.root.winfo_height()

        # 计算对话框应该显示的位置（居中于主窗口）
        x = main_x + (main_width - dialog_width) // 2
        y = main_y + (main_height - dialog_height) // 2

        # 设置对话框位置
        settings_window.geometry(f"+{x}+{y}")

    def create_window_size_settings(self, parent_frame):
        """创建窗口大小设置界面"""
        # 获取屏幕尺寸
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 获取当前窗口尺寸
        current_width = self.root.winfo_width()
        current_height = self.root.winfo_height()

        # 计算当前窗口占屏幕的百分比
        width_percent = int(current_width / screen_width * 100)
        height_percent = int(current_height / screen_height * 100)

        # 创建标签和滑块
        ttk.Label(parent_frame, text="窗口宽度 (占屏幕百分比):").pack(anchor="w", padx=10, pady=5)
        width_var = tk.IntVar(value=width_percent)
        width_scale = ttk.Scale(parent_frame, from_=20, to=100, variable=width_var, orient="horizontal")
        width_scale.pack(fill="x", padx=10, pady=5)
        width_label = ttk.Label(parent_frame, text=f"{width_percent}% ({current_width}像素)")
        width_label.pack(anchor="w", padx=10)

        ttk.Label(parent_frame, text="窗口高度 (占屏幕百分比):").pack(anchor="w", padx=10, pady=5)
        height_var = tk.IntVar(value=height_percent)
        height_scale = ttk.Scale(parent_frame, from_=20, to=100, variable=height_var, orient="horizontal")
        height_scale.pack(fill="x", padx=10, pady=5)
        height_label = ttk.Label(parent_frame, text=f"{height_percent}% ({current_height}像素)")
        height_label.pack(anchor="w", padx=10)

        # 更新标签显示
        def update_labels(*args):
            new_width = int(screen_width * width_var.get() / 100)
            new_height = int(screen_height * height_var.get() / 100)
            width_label.config(text=f"{width_var.get()}% ({new_width}像素)")
            height_label.config(text=f"{height_var.get()}% ({new_height}像素)")

        width_var.trace_add("write", update_labels)
        height_var.trace_add("write", update_labels)

        # 应用按钮
        def apply_size():
            new_width = int(screen_width * width_var.get() / 100)
            new_height = int(screen_height * height_var.get() / 100)

            # 计算窗口位置，使其居中
            x_pos = (screen_width - new_width) // 2
            y_pos = (screen_height - new_height) // 2

            # 设置窗口大小和位置
            self.root.geometry(f"{new_width}x{new_height}+{x_pos}+{y_pos}")

            # 保存窗口大小设置到配置文件
            self.save_window_size_config(width_var.get(), height_var.get())

            messagebox.showinfo("成功", "窗口大小已调整")

        # 预设按钮
        preset_frame = ttk.Frame(parent_frame)
        preset_frame.pack(fill="x", padx=10, pady=5)

        def set_preset(w_percent, h_percent):
            width_var.set(w_percent)
            height_var.set(h_percent)

        ttk.Button(preset_frame, text="75%", command=lambda: set_preset(75, 75)).pack(side="left", padx=5)
        ttk.Button(preset_frame, text="85%", command=lambda: set_preset(85, 85)).pack(side="left", padx=5)
        ttk.Button(preset_frame, text="100%", command=lambda: set_preset(100, 100)).pack(side="left", padx=5)

        ttk.Button(parent_frame, text="应用", command=apply_size).pack(anchor="center", pady=10)

    def create_theme_settings(self, parent_frame):
        """创建主题设置界面"""
        ttk.Label(parent_frame, text="应用主题:").pack(anchor="w", padx=10, pady=5)

        # 显示当前的主题
        current_theme_name = self.theme_names.get(self.current_theme.get(), "默认主题")
        current_theme_label = ttk.Label(parent_frame, text=f"当前主题: {current_theme_name}")
        current_theme_label.pack(anchor="w", padx=10, pady=5)

        # 创建单选按钮框架
        theme_frame = ttk.Frame(parent_frame)
        theme_frame.pack(fill="x", padx=10, pady=5)

        # 为每个主题创建单选按钮
        for theme in self.themes:
            theme_name = self.theme_names.get(theme, theme)
            rb = ttk.Radiobutton(
                theme_frame,
                text=theme_name,
                value=theme,
                variable=self.current_theme
            )
            rb.pack(anchor="w", padx=20, pady=2)
        # 显示主题描述
        theme_desc_frame = ttk.LabelFrame(parent_frame, text="主题说明")
        theme_desc_frame.pack(fill="x", padx=10, pady=5)

        theme_descriptions = {
            "default": "系统默认主题",
            "alt": "替代样式主题",
            "clam": "简洁风格主题",
            "classic": "传统经典主题",
            "high_contrast": "高对比度主题，适合长时间工作，减轻视觉疲劳",
            "eye_comfort": "护眼模式，使用柔和色调，降低蓝光刺激"
        }
        theme_desc_label = ttk.Label(theme_desc_frame, text=theme_descriptions.get(self.current_theme.get(), ""))
        theme_desc_label.pack(anchor="w", padx=10, pady=5)
        # 当选择主题更改时更新描述
        def update_description(*args):
            selected_theme = self.current_theme.get()
            theme_desc_label.config(text=theme_descriptions.get(selected_theme, ""))

        self.current_theme.trace_add("write", update_description)
         # 应用主题按钮
        def apply_theme():
            selected_theme = self.current_theme.get()
            style = ttk.Style()

            # 先应用基础主题
            if selected_theme in ["default", "alt", "clam", "classic"]:
                style.theme_use(selected_theme)
            else:
                # 对于自定义主题，我们基于默认主题进行配置
                style.theme_use("default")

            # 应用自定义主题配置
            if selected_theme == "high_contrast":
                self.apply_high_contrast_theme(style)
            elif selected_theme == "eye_comfort":
                self.apply_eye_comfort_theme(style)

            # 更新当前主题标签
            current_theme_name = self.theme_names.get(selected_theme, selected_theme)
            current_theme_label.config(text=f"当前主题: {current_theme_name}")

            # 保存主题设置
            self.save_theme_config(selected_theme)

            # 刷新所有组件的颜色
            self.refresh_all_velocities_colors()

            messagebox.showinfo("成功", f"已应用{current_theme_name}")

        ttk.Button(parent_frame, text="应用主题", command=apply_theme).pack(anchor="center", pady=10)

        # 主题预览区域
        preview_frame = ttk.LabelFrame(parent_frame, text="主题预览")
        preview_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # 创建简单的预览示例
        preview_content = ttk.Frame(preview_frame)
        preview_content.pack(fill="both", expand=True, padx=5, pady=5)

        # 预览中的示例元素
        ttk.Label(preview_content, text="标签示例").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(preview_content).pack(fill="x", padx=5, pady=5)
        ttk.Button(preview_content, text="按钮示例").pack(anchor="w", padx=5, pady=5)
        ttk.Checkbutton(preview_content, text="复选框示例").pack(anchor="w", padx=5, pady=5)
        ttk.Combobox(preview_content, values=["下拉框示例"]).pack(fill="x", padx=5, pady=5)

        # 添加流速颜色示例
        velocity_frame = ttk.LabelFrame(preview_content, text="流速颜色示例")
        velocity_frame.pack(fill="x", padx=5, pady=5)

        ttk.Label(velocity_frame, text="正常范围:", foreground="#009900").pack(side="left", padx=10)
        ttk.Label(velocity_frame, text="18.50", foreground="#009900").pack(side="left", padx=5)

        ttk.Label(velocity_frame, text="注意范围:", foreground="#ff9900").pack(side="left", padx=10)
        ttk.Label(velocity_frame, text="23.45", foreground="#ff9900").pack(side="left", padx=5)

        ttk.Label(velocity_frame, text="危险范围:", foreground="#cc0000").pack(side="left", padx=10)
        ttk.Label(velocity_frame, text="28.75", foreground="#cc0000").pack(side="left", padx=5)

    # 添加高对比度主题应用方法
    def apply_high_contrast_theme(self, style):
        """应用高对比度主题配置"""
        # 设置背景色和前景色
        style.configure(".",
                    background="#FFFFFF",
                    foreground="#000000",
                    fieldbackground="#FFFFFF")

        # 设置按钮样式
        style.configure("TButton",
                    background="#E0E0E0",
                    foreground="#000000",
                    focuscolor="#0078D7")

        # 设置标签样式
        style.configure("TLabel",
                    background="#FFFFFF",
                    foreground="#000000")

        # 设置输入框样式
        style.configure("TEntry",
                    fieldbackground="#FFFFFF",
                    foreground="#000000",
                    selectbackground="#0078D7",
                    selectforeground="#FFFFFF")

        # 设置下拉框样式
        style.configure("TCombobox",
                    fieldbackground="#FFFFFF",
                    background="#FFFFFF",
                    foreground="#000000",
                    selectbackground="#0078D7",
                    selectforeground="#FFFFFF")
        # 设置框架样式
        style.configure("TFrame", background="#FFFFFF")
        style.configure("TLabelframe", background="#FFFFFF")
        style.configure("TLabelframe.Label", background="#FFFFFF", foreground="#000000")

        # 设置选项卡样式
        style.configure("TNotebook", background="#FFFFFF", tabmargins=[2, 5, 2, 0])
        style.configure("TNotebook.Tab",
                    background="#E0E0E0",
                    foreground="#000000",
                    padding=[10, 2])
        style.map("TNotebook.Tab",
                background=[("selected", "#FFFFFF"), ("active", "#F0F0F0")],
                foreground=[("selected", "#000000"), ("active", "#000000")])

        # 更新流速颜色方案，提高对比度
        self.velocity_colors = {
            "normal": "#006600",  # 更深的绿色
            "warning": "#CC6600",  # 更深的橙色
            "danger": "#990000"   # 更深的红色
        }

    # 添加护眼模式主题应用方法
    def apply_eye_comfort_theme(self, style):
        """应用护眼模式主题配置"""
        # 设置柔和的护眼色调
        bg_color = "#F5F5DC"  # 米色背景
        fg_color = "#333333"  # 深灰色文字

        # 设置背景色和前景色
        style.configure(".",
                    background=bg_color,
                    foreground=fg_color,
                    fieldbackground=bg_color)

        # 设置按钮样式
        style.configure("TButton",
                    background="#D8D8C0",
                    foreground=fg_color)

        # 设置标签样式
        style.configure("TLabel",
                    background=bg_color,
                    foreground=fg_color)

        # 设置输入框样式
        style.configure("TEntry",
                    fieldbackground="#FFFFFF",
                    foreground=fg_color)

        # 设置下拉框样式
        style.configure("TCombobox",
                    fieldbackground="#FFFFFF",
                    background=bg_color,
                    foreground=fg_color)
        # 设置框架样式
        style.configure("TFrame", background=bg_color)
        style.configure("TLabelframe", background=bg_color)
        style.configure("TLabelframe.Label", background=bg_color, foreground=fg_color)

        # 设置选项卡样式
        style.configure("TNotebook", background=bg_color)
        style.configure("TNotebook.Tab",
                    background="#E6E6D8",
                    foreground=fg_color,
                    padding=[10, 2])
        style.map("TNotebook.Tab",
                background=[("selected", bg_color), ("active", "#EFEFEF")],
                foreground=[("selected", fg_color), ("active", fg_color)])

        # 更新流速颜色方案，使用对眼睛更友好的色调
        self.velocity_colors = {
            "normal": "#2E8B57",  # 海洋绿
            "warning": "#CD853F",  # 秘鲁色
            "danger": "#B22222"    # 砖红色
        }

    # 修改create_other_settings方法，添加设置历史文件路径的功能
    def create_other_settings(self, parent_frame):
        """创建其他设置界面"""
        # 历史文件路径设置
        history_file_frame = ttk.LabelFrame(parent_frame, text="历史文件设置")
        history_file_frame.pack(fill="x", padx=10, pady=10)

        ttk.Label(history_file_frame, text="当前历史文件路径:").pack(anchor="w", padx=10, pady=5)

        # 显示当前历史文件路径
        path_var = tk.StringVar(value=self.history_file)
        path_entry = ttk.Entry(history_file_frame, textvariable=path_var, width=40)
        path_entry.pack(fill="x", padx=10, pady=5)
        path_entry.config(state="readonly")  # 设置为只读

        # 创建按钮框架
        btn_frame = ttk.Frame(history_file_frame)
        btn_frame.pack(fill="x", padx=10, pady=5)

        # 选择文件夹按钮
        def select_folder():
            folder_path = filedialog.askdirectory(title="选择历史文件保存目录")
            if folder_path:
                new_history_path = os.path.join(folder_path, 'history.json')
                path_var.set(new_history_path)
                self.custom_history_path = new_history_path

        # 重置为默认按钮
        def reset_to_default():
            default_path = os.path.join(self.app_data_dir, 'history.json')
            path_var.set(default_path)
            self.custom_history_path = ''

        # 应用按钮
        def apply_path():
            new_path = path_var.get()

            # 检查目录是否存在
            dir_path = os.path.dirname(new_path)
            if not os.path.exists(dir_path):
                try:
                    os.makedirs(dir_path)
                except Exception as e:
                    messagebox.showerror("错误", f"无法创建目录: {str(e)}")
                    return

            # 如果新旧路径不同，且旧文件存在，询问是否复制
            if new_path != self.history_file and os.path.exists(self.history_file):
                if messagebox.askyesno("确认", "是否将现有历史数据复制到新位置?"):
                    try:
                        # 确保目标目录存在
                        os.makedirs(os.path.dirname(new_path), exist_ok=True)

                        # 读取旧文件
                        with open(self.history_file, 'r', encoding='utf-8') as f:
                            history_data = json.load(f)

                        # 写入新文件
                        with open(new_path, 'w', encoding='utf-8') as f:
                            json.dump(history_data, f, ensure_ascii=False, indent=2)

                        messagebox.showinfo("成功", "历史数据已复制到新位置")
                    except Exception as e:
                        messagebox.showerror("错误", f"复制历史数据失败: {str(e)}")
                        return

            # 更新历史文件路径
            self.history_file = new_path
            self.custom_history_path = new_path
                # 保存配置
            self.save_config()

            # 刷新历史记录显示
            self.update_history_display()

            messagebox.showinfo("成功", "历史文件路径已更新")

        ttk.Button(btn_frame, text="选择目录", command=select_folder).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="恢复默认", command=reset_to_default).pack(side="left", padx=5)
        ttk.Button(btn_frame, text="应用", command=apply_path).pack(side="right", padx=5)

        # 其他设置项
        ttk.Label(parent_frame, text="更多设置将在未来版本中添加").pack(anchor="center", pady=20)

    def save_theme_config(self, theme_name):
        """保存主题设置到配置文件"""
        try:
            config_dir = os.path.dirname(self.history_file)
            config_file = os.path.join(config_dir, "window_config.json")

            # 读取现有配置（如果存在）
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                config = {}

            # 更新主题设置
            config["theme"] = theme_name

            # 保存回配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False)
        except Exception as e:
            print(f"保存主题配置时出错: {str(e)}")

    def show_resize_window(self):
        """显示窗口大小调整对话框"""
        # 为了向后兼容，调用设置窗口并默认选择窗口大小选项卡
        self.show_settings_window()

    def save_window_size_config(self, width_percent, height_percent):
        """保存窗口大小设置到配置文件"""
        try:
            config_dir = os.path.dirname(self.history_file)
            config_file = os.path.join(config_dir, "window_config.json")

            config = {
                "width_percent": width_percent,
                "height_percent": height_percent
            }

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False)
        except Exception as e:
            print(f"保存窗口配置时出错: {str(e)}")

    def load_window_size_config(self):
        """加载窗口大小设置"""
        try:
            config_dir = os.path.dirname(self.history_file)
            config_file = os.path.join(config_dir, "window_config.json")

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                width_percent = config.get("width_percent", 85)
                height_percent = config.get("height_percent", 85)

                # 加载主题设置
                theme = config.get("theme", "default")
                if theme in self.themes:
                    self.current_theme.set(theme)
                    style = ttk.Style()
                    # 应用基础主题或自定义主题
                    if theme in ["default", "alt", "clam", "classic"]:
                        style.theme_use(theme)
                    else:
                        # 对于自定义主题，我们基于默认主题进行配置
                        style.theme_use("default")

                        # 应用自定义主题配置
                        if theme == "high_contrast":
                            self.apply_high_contrast_theme(style)
                        elif theme == "eye_comfort":
                            self.apply_eye_comfort_theme(style)

                # 获取屏幕尺寸
                screen_width = self.root.winfo_screenwidth()
                screen_height = self.root.winfo_screenheight()

                # 计算窗口大小
                window_width = int(screen_width * width_percent / 100)
                window_height = int(screen_height * height_percent / 100)

                # 计算窗口位置，使其居中
                x_pos = (screen_width - window_width) // 2
                y_pos = (screen_height - window_height) // 2

                # 设置窗口大小和位置
                self.root.geometry(f"{window_width}x{window_height}+{x_pos}+{y_pos}")
                # 刷新所有组件的颜色
                self.refresh_all_velocities_colors()
        except Exception as e:
            print(f"加载窗口配置时出错: {str(e)}")
    def refresh_all_calculations(self):
        """刷新所有计算结果"""
        try:
            # 显示刷新开始提示
            self.root.config(cursor="wait")  # 更改鼠标光标为等待状态

            # 执行所有计算
            self.auto_calculate_main_pipe()  # 计算总管管径
            self.auto_calculate_bypass_pipe()  # 计算支通路管径
            self.calculate_furnace_values()  # 计算小炉值

            # 如果有阀门计算窗口打开，也刷新阀门计算
            if hasattr(self, 'main_valve_c_max') and self.main_valve_c_max.get():
                self.calculate_main_valve_c_values()
                self.calculate_main_valve_k_values()
                self.calculate_release_valve_area()
                for i in range(len(self.small_valve_data) if hasattr(self, 'small_valve_data') else 0):
                    self.calculate_small_valve_c_values(i)
                    self.calculate_small_valve_k_values(i)

            # 恢复鼠标光标
            self.root.config(cursor="")

            # 显示刷新完成提示
            messagebox.showinfo("刷新完成", "所有计算结果已更新！")

        except Exception as e:
            # 恢复鼠标光标
            self.root.config(cursor="")
            messagebox.showerror("刷新错误", f"刷新计算时出错: {str(e)}")

    def create_tab_interface(self):
        """创建5栏式界面，按照截图布局"""
        # 创建一个框架用于放置所有内容
        self.main_frame = ttk.Frame(self.work_area)
        self.work_area.add(self.main_frame)

        # 创建5个区域的框架
        self.project_info_frame = ttk.LabelFrame(self.main_frame, text="项目信息栏")
        self.flow_frame = ttk.LabelFrame(self.main_frame, text="流量栏")
        self.pressure_frame = ttk.LabelFrame(self.main_frame, text="压力栏")
        self.diameter_frame = ttk.LabelFrame(self.main_frame, text="选取管径栏")
        self.velocity_temp_frame = ttk.LabelFrame(self.main_frame, text="流速及温度栏")

        # 使用grid布局管理器排列这些框架
        self.project_info_frame.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        self.flow_frame.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        self.pressure_frame.grid(row=0, column=2, padx=5, pady=5, sticky="nsew")
        self.diameter_frame.grid(row=0, column=3, padx=5, pady=5, sticky="nsew")
        self.velocity_temp_frame.grid(row=0, column=4, padx=5, pady=5, sticky="nsew")

        # 配置列的权重，使其平均分配空间
        for i in range(5):
            self.main_frame.grid_columnconfigure(i, weight=1)

        # 在各个区域中创建内容
        self.create_project_info_content()
        self.create_flow_content()
        self.create_pressure_content()
        self.create_diameter_content()
        self.create_velocity_temp_content()

    def create_project_info_content(self):
        """创建项目信息区域的内容"""
        # 创建输入字段
        ttk.Label(self.project_info_frame, text="工程名称:").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.project_info_frame, textvariable=self.project_name).pack(fill=tk.X, padx=5, pady=1)
        # 【新增】工程代号输入框
        ttk.Label(self.project_info_frame, text="工程代号:").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.project_info_frame, textvariable=self.project_code).pack(fill=tk.X, padx=5, pady=1)
        # 【新增】添加提示标签
        ttk.Label(self.project_info_frame, text="注意: 工程名称和工程代号均为唯一标识，\n相同则会覆盖原有记录",
                 foreground="red").pack(anchor="w", padx=5, pady=1)


        # 绑定小炉数变化事件
        self.furnace_count.trace_add("write", self.update_furnace_display)

        ttk.Label(self.project_info_frame, text="项目类型:").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.project_info_frame, textvariable=self.project_type).pack(fill=tk.X, padx=5, pady=1)
        # 【新增】吨位输入框
        ttk.Label(self.project_info_frame, text="吨位(t/d):").pack(anchor="w", padx=5, pady=1)
        self.daily_capacity = tk.StringVar(value="")
        ttk.Entry(self.project_info_frame, textvariable=self.daily_capacity).pack(fill=tk.X, padx=5, pady=1)
        ttk.Label(self.project_info_frame, text="小炉数:").pack(anchor="w", padx=5, pady=1)
        furnace_entry = ttk.Entry(self.project_info_frame, textvariable=self.furnace_count)
        furnace_entry.pack(fill=tk.X, padx=5, pady=2)


        ttk.Label(self.project_info_frame, text="一窑几线:").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.project_info_frame, textvariable=self.line_count).pack(fill=tk.X, padx=5, pady=1)

    def create_flow_content(self):
        """创建流量区域的内容"""
        ttk.Label(self.flow_frame, text="窑老期流量(Nm³/h):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.flow_frame, textvariable=self.old_flow).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(self.flow_frame, text="正常生产时流量(Nm³/h):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.flow_frame, textvariable=self.normal_flow).pack(fill=tk.X, padx=5, pady=2)
        # 【新增】支通路总流量输入字段
        ttk.Label(self.flow_frame, text="支通路总流量(Nm³/h):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.flow_frame, textvariable=self.bypass_total_flow).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(self.flow_frame, text="成型室加热流量(Nm³/h):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.flow_frame, textvariable=self.forming_flow).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(self.flow_frame, text="边火加热流量(Nm³/h):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.flow_frame, textvariable=self.edge_flow).pack(fill=tk.X, padx=5, pady=2)

        # 添加变量跟踪，当流量变化时自动计算
        self.old_flow.trace_add("write", self.auto_calculate_main_pipe)
        self.normal_flow.trace_add("write", self.auto_calculate_main_pipe)
        self.bypass_total_flow.trace_add("write", self.auto_calculate_bypass_pipe)  # 【新增】支通路流量变化跟踪
        self.edge_flow.trace_add("write", self.auto_calculate_bypass_pipe)  # 【新增】边火加热流量变化跟踪
        # 【修改】添加流量变化时自动计算小炉值
        self.old_flow.trace_add("write", self.calculate_furnace_values)
        self.normal_flow.trace_add("write", self.calculate_furnace_values)
        # 添加窑老期流量变化时更新设备表
        self.old_flow.trace_add("write", self.update_equipment_diameter)


    def create_pressure_content(self):
        """创建压力区域的内容"""
        ttk.Label(self.pressure_frame, text="进车间压力(MPa):").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.pressure_frame, textvariable=self.inlet_pressure).pack(fill=tk.X, padx=5, pady=1)

        ttk.Label(self.pressure_frame, text="总管调节阀前压力(MPa):").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.pressure_frame, textvariable=self.main_valve_pre).pack(fill=tk.X, padx=5, pady=1)

        ttk.Label(self.pressure_frame, text="总管调节阀后压力(MPa):").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.pressure_frame, textvariable=self.main_valve_post).pack(fill=tk.X, padx=5, pady=1)

        ttk.Label(self.pressure_frame, text="小炉支管调节阀前压力(MPa):").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.pressure_frame, textvariable=self.branch_valve_pre).pack(fill=tk.X, padx=5, pady=1)

        ttk.Label(self.pressure_frame, text="小炉支管调节阀后压力(MPa):").pack(anchor="w", padx=5, pady=1)
        ttk.Entry(self.pressure_frame, textvariable=self.branch_valve_post).pack(fill=tk.X, padx=5, pady=1)
        # 【新增】支通路总管阀前压力
        ttk.Label(self.pressure_frame, text="支通路总管阀前压力(MPa):").pack(anchor="w", padx=5, pady=1)  # <-- 新增
        ttk.Entry(self.pressure_frame, textvariable=self.bypass_main_valve_pre).pack(fill=tk.X, padx=5, pady=1)  # <-- 新增

        # 【新增】支通路总管阀后压力
        ttk.Label(self.pressure_frame, text="支通路总管阀后压力(MPa):").pack(anchor="w", padx=5, pady=1)  # <-- 新增
        ttk.Entry(self.pressure_frame, textvariable=self.bypass_main_valve_post).pack(fill=tk.X, padx=5, pady=1)  # <-- 新增
        # 添加变量跟踪，当压力变化时自动计算
        self.main_valve_pre.trace_add("write", self.auto_calculate_main_pipe)
        self.main_valve_post.trace_add("write", self.auto_calculate_main_pipe)
        # 【新增】添加支通路压力变化跟踪
        self.bypass_main_valve_pre.trace_add("write", self.auto_calculate_bypass_pipe)  # <-- 新增
        self.bypass_main_valve_post.trace_add("write", self.auto_calculate_bypass_pipe)  # <-- 新增
        # 【新增】添加小炉支管压力变化时自动计算小炉值
        self.branch_valve_pre.trace_add("write", lambda *args: self.calculate_furnace_values())
        self.branch_valve_post.trace_add("write", lambda *args: self.calculate_furnace_values())


    def create_diameter_content(self):
        """创建管径选取区域的内容"""
        ttk.Label(self.diameter_frame, text="总管阀前管径选取(mm):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.diameter_frame, textvariable=self.main_pre_diameter).pack(fill=tk.X, padx=5, pady=2)
        # 绑定变量跟踪，当self.main_pre_diameter变化时同步到self.main_pre_selected
        self.main_pre_diameter.trace_add("write", self.sync_main_pre_diameter)

        ttk.Label(self.diameter_frame, text="总管阀后管径选取(mm):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.diameter_frame, textvariable=self.main_post_diameter).pack(fill=tk.X, padx=5, pady=2)
         # 绑定变量跟踪，当self.main_post_diameter变化时同步到self.main_post_selected
        self.main_post_diameter.trace_add("write", self.sync_main_post_diameter)
         # 添加变量跟踪，当self.main_pre_diameter变化时自动计算反算流速 - 【新增代码】
         # 添加对设备表管径的更新监听
        self.main_pre_diameter.trace_add("write", self.update_equipment_diameter)
        self.main_pre_diameter.trace_add("write", self.auto_calculate_main_pre_velocity)
        ttk.Label(self.diameter_frame, text="支通路总管阀前管径选取(mm):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.diameter_frame, textvariable=self.bypass_main_pre_diameter).pack(fill=tk.X, padx=5, pady=2)
        # 绑定变量跟踪，当self.bypass_main_pre_diameter变化时同步到self.bypass_vars['main_pre']['selected']
        self.bypass_main_pre_diameter.trace_add("write", self.sync_bypass_main_pre_diameter)
        # 在第6696行左右，在同步代码之后添加
        self.bypass_main_pre_diameter.trace_add("write", self.update_equipment_diameter)
        # 添加变量跟踪，当self.main_post_diameter变化时自动计算反算流速 - 【新增代码】
        self.main_post_diameter.trace_add("write", self.auto_calculate_main_post_velocity)

        ttk.Label(self.diameter_frame, text="支通路总管阀后管径选取(mm):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.diameter_frame, textvariable=self.bypass_main_post_diameter).pack(fill=tk.X, padx=5, pady=2)
        # 绑定变量跟踪，当self.bypass_main_post_diameter变化时同步到self.bypass_vars['main_post']['selected']
        self.bypass_main_post_diameter.trace_add("write", self.sync_bypass_main_post_diameter)

    def create_velocity_temp_content(self):
        """创建流速及温度区域的内容"""
        ttk.Label(self.velocity_temp_frame, text="天然气总管流速 (m/s):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.velocity_temp_frame, textvariable=self.main_velocity).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(self.velocity_temp_frame, text="小炉支管流速 (m/s):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.velocity_temp_frame, textvariable=self.branch_velocity).pack(fill=tk.X, padx=5, pady=2)

        ttk.Label(self.velocity_temp_frame, text="设计温度 (℃):").pack(anchor="w", padx=5, pady=5)
        ttk.Entry(self.velocity_temp_frame, textvariable=self.temperature).pack(fill=tk.X, padx=5, pady=2)
        # 添加0#氧枪选项
        oxygen_lance_frame = ttk.Frame(self.velocity_temp_frame)
        oxygen_lance_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(oxygen_lance_frame, text="是否有0#氧枪:").pack(side="left", padx=5)

        # 创建选项变量
        self.has_oxygen_lance = tk.StringVar(value="是")

        # 创建单选按钮
        ttk.Radiobutton(oxygen_lance_frame, text="是", variable=self.has_oxygen_lance, value="是",
                        command=self.update_oxygen_tools_state).pack(side="left", padx=5)
        ttk.Radiobutton(oxygen_lance_frame, text="否", variable=self.has_oxygen_lance, value="否",
                        command=self.update_oxygen_tools_state).pack(side="left", padx=5)

        # 添加全氧窑选项
        oxygen_kiln_frame = ttk.Frame(self.velocity_temp_frame)
        oxygen_kiln_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(oxygen_kiln_frame, text="是否是全氧窑:").pack(side="left", padx=5)

        # 创建选项变量
        self.is_oxygen_kiln = tk.StringVar(value="否")

        # 创建单选按钮
        ttk.Radiobutton(oxygen_kiln_frame, text="是", variable=self.is_oxygen_kiln, value="是",
                        command=self.update_oxygen_tools_state).pack(side="left", padx=5)
        ttk.Radiobutton(oxygen_kiln_frame, text="否", variable=self.is_oxygen_kiln, value="否",
                        command=self.update_oxygen_tools_state).pack(side="left", padx=5)
        # 添加变量跟踪，当流速或温度变化时自动计算
        self.main_velocity.trace_add("write", self.auto_calculate_main_pipe)
        self.temperature.trace_add("write", self.auto_calculate_main_pipe)
        # 添加温度变化跟踪，当温度变化时自动计算支通路管径
        self.temperature.trace_add("write", self.auto_calculate_bypass_pipe)
         # 【新增】添加流速和温度变化时自动计算小炉值
        self.branch_velocity.trace_add("write", lambda *args: self.calculate_furnace_values)
        self.temperature.trace_add("write", self.calculate_furnace_values)
    # 添加新的自动计算方法
    # 【新增方法】计算小炉区的值
    def calculate_furnace_values(self, *args):
        """
        计算小炉值

        Args:
            *args: 用于接收Tkinter事件回调传递的额外参数，实际未使用
        """
        """当流量、压力、温度或流速变化时，自动计算小炉区的相关值"""
        try:
            # 获取基础数据
            old_flow = float(self.old_flow.get() or 0)  # 窑老期流量
            normal_flow = float(self.normal_flow.get() or 0)  # 正常生产时流量
            branch_pre_pressure = float(self.branch_valve_pre.get() or 0.1)  # 小炉支管调节阀前压力
            branch_post_pressure = float(self.branch_valve_post.get() or 0.1)  # 小炉支管调节阀后压力
            branch_velocity = float(self.branch_velocity.get() or 1)  # 小炉支管流速
            temperature = float(self.temperature.get() or 0)  # 环境温度

            # 遍历所有小炉
            for furnace in self.furnace_data:
                try:
                    # 获取小炉特定数据
                    nozzle_count = int(furnace['nozzle_count'].get() or 0)
                    heat_load = float(furnace['heat_load'].get() or 0)
                    float_value = float(furnace['float_value'].get() or 0)

                    if len(self.furnace_data) > 0:  # 确保有小炉数据
                        # 修正公式：最大流量 = 窑老期流量 * (平均热负荷百分数 + 浮动值百分数) / 100
                        max_flow = old_flow * (heat_load + float_value) / 100
                        furnace['max_flow'].configure(text=f"{max_flow:.2f}" if max_flow > 0 else "", foreground="#0000FF")

                        # 修正公式：正常流量 = 正常生产时流量 * 平均热负荷百分数 / 100
                        normal_flow_value = normal_flow * heat_load / 100
                        furnace['normal_flow'].configure(text=f"{normal_flow_value:.2f}" if normal_flow_value > 0 else "", foreground="#0000FF")

                        # 修正公式：最小流量 = 正常生产时流量 * (平均热负荷百分数 - 浮动值百分数) / 100
                        min_flow = normal_flow * (heat_load - float_value) / 100
                        # 确保最小流量不为负数
                        min_flow = max(0, min_flow)
                        furnace['min_flow'].configure(text=f"{min_flow:.2f}" if min_flow > 0 else "")

                        # 计算阀前管径 = 18.8 * sqrt(最大流量 * (273 + 设计温度) / 2730 / (小炉支管调节阀前压力+0.1) / 小炉支管流速)
                        if branch_pre_pressure > 0 and branch_velocity > 0:
                            calc_diameter = 18.8 * math.sqrt(
                                (max_flow * (273 + temperature) / 2730 / (branch_pre_pressure+0.1))
                                / branch_velocity
                            )
                            furnace['calc_diameter'].configure(text=f"{calc_diameter:.2f}" if calc_diameter > 0 else "", foreground="#0000FF")

                        # 计算阀后管径 = 18.8 * sqrt(最大流量 * (273 + 设计温度) / 2730 / (小炉支管调节阀后压力+0.1) / 小炉支管流速)
                        if branch_post_pressure > 0 and branch_velocity > 0:
                            calc_post_diameter = 18.8 * math.sqrt(
                                (max_flow * (273 + temperature) / 2730 / (branch_post_pressure+0.1))
                                / branch_velocity
                            )
                            furnace['calc_post_diameter'].configure(text=f"{calc_post_diameter:.2f}" if calc_post_diameter > 0 else "", foreground="#0000FF")

                        # 计算反算阀前流速
                        selected_diameter = float(furnace['selected_diameter'].get() or 0)
                        if selected_diameter > 0 and branch_pre_pressure > 0:
                            calc_velocity = max_flow * (273 + temperature) / 2730 / (branch_pre_pressure+0.1) / \
                                         (selected_diameter / 18.8) / (selected_diameter / 18.8)
                            furnace['calc_velocity'].configure(text=f"{calc_velocity:.2f}" if calc_velocity > 0 else "")

                        # 计算反算阀后流速
                        selected_post_diameter = float(furnace['selected_post_diameter'].get() or 0)
                        if selected_post_diameter > 0 and branch_post_pressure > 0:
                            calc_post_velocity = max_flow * (273 + temperature) / 2730 / (branch_post_pressure+0.1) / \
                                              (selected_post_diameter / 18.8) / (selected_post_diameter / 18.8)
                            furnace['calc_post_velocity'].configure(text=f"{calc_post_velocity:.2f}" if calc_post_velocity > 0 else "")
                except Exception as e:
                    # 单个小炉计算出错时继续处理其他小炉
                    continue

        except Exception as e:
            # 出错时不显示错误消息，保持静默
            pass
    def auto_calculate_main_pipe(self, *args):
        """自动计算总管调节阀前后管径"""
        try:
            # 获取输入值
            try:
                old_flow = float(self.old_flow.get() or 0)
                normal_flow = float(self.normal_flow.get() or 0)
                main_pre_pressure = float(self.main_valve_pre.get() or 0.1)  # 总管调节阀前压力
                main_post_pressure = float(self.main_valve_post.get() or 0.1)  # 总管调节阀后压力
                temperature = float(self.temperature.get() or 0)
                main_velocity = float(self.main_velocity.get() or 1)  # 默认值1m/s防止除零

                # 计算总管调节阀前管径
                # 新公式: 18.8*sqrt(窑老期流量*（273+设计温度）/2730/总管调节阀前压力/天然气总管流速)
                if old_flow > 0 and main_pre_pressure > 0 and main_velocity > 0:
                    main_pre_diameter = 18.8 * math.sqrt(
                        (old_flow * (273 + temperature) / 2730 / (main_pre_pressure+0.1))
                        / main_velocity
                    )
                    self.main_pre_calc_diameter.configure(text=f"{main_pre_diameter:.2f}" if main_pre_diameter > 0 else "")

                # 计算总管调节阀后管径
                # 新公式: 18.8*sqrt(窑老期流量*（273+设计温度）/2730/总管调节阀后压力/天然气总管流速)
                if old_flow > 0 and main_post_pressure > 0 and main_velocity > 0:
                    main_post_diameter = 18.8 * math.sqrt(
                        (old_flow * (273 + temperature) / 2730 / (main_post_pressure+0.1))
                        / main_velocity
                    )
                    self.main_post_calc_diameter.configure(text=f"{main_post_diameter:.2f}" if main_post_diameter > 0 else "")

                # 计算反算流速
                selected_pre_diameter = float(self.main_pre_selected.get() or 0)
                if selected_pre_diameter > 0 and main_pre_pressure > 0:
                    # 修改反算总管阀前流速计算公式
                    pre_velocity = old_flow * (273 + temperature) / 2730 / (main_pre_pressure+0.1) / \
                                 (selected_pre_diameter / 18.8) / (selected_pre_diameter / 18.8)
                    self.main_pre_velocity.configure(text=f"{pre_velocity:.2f}" if pre_velocity > 0 else "")

                selected_post_diameter = float(self.main_post_selected.get() or 0)
                if selected_post_diameter > 0 and main_post_pressure > 0:
                    # 修改反算总管阀后流速计算公式
                    post_velocity = old_flow * (273 + temperature) / 2730 / (main_post_pressure+0.1) / \
                                  (selected_post_diameter / 18.8) / (selected_post_diameter / 18.8)
                    self.main_post_velocity.configure(text=f"{post_velocity:.2f}" if post_velocity > 0 else "")

            except ValueError:
                pass

        except Exception as e:
            # 出错时不显示错误消息，保持静默
            pass
     # 【新增方法】
    def auto_calculate_main_pre_velocity(self, *args):
        """自动计算总管阀前反算流速"""
        try:
            old_flow = float(self.old_flow.get() or 0)
            main_pre_pressure = float(self.main_valve_pre.get() or 0.1)  # 使用正确的变量名
            temperature = float(self.temperature.get() or 0)
            selected_pre_diameter = float(self.main_pre_diameter.get() or 0)

            if selected_pre_diameter > 0 and main_pre_pressure > 0:
                # 修改反算总管阀前流速计算公式
                pre_velocity = old_flow * (273 + temperature) / 2730 / (main_pre_pressure+0.1)/ \
                             (selected_pre_diameter / 18.8) / (selected_pre_diameter / 18.8)
                # 根据流速范围设置不同颜色
                velocity_text = f"{pre_velocity:.2f}"

                # 设置颜色基于流速值
                if pre_velocity <= 20:
                    self.main_pre_velocity.configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                elif pre_velocity <= 25:
                    self.main_pre_velocity.configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                else:
                    self.main_pre_velocity.configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
            else:
                self.main_pre_velocity.configure(text="")
        except ValueError:
            self.main_pre_velocity.configure(text="")
    # 【新增方法】
    def auto_calculate_main_post_velocity(self, *args):
        """自动计算总管阀后反算流速"""
        try:
            old_flow = float(self.old_flow.get() or 0)  # 使用窑老期流量
            main_post_pressure = float(self.main_valve_post.get() or 0.1)  # 使用总管调节阀后压力
            temperature = float(self.temperature.get() or 0)
            selected_post_diameter = float(self.main_post_diameter.get() or 0)

            if selected_post_diameter > 0 and main_post_pressure > 0:

                # 修改反算总管阀后流速计算公式
                post_velocity = old_flow * (273 + temperature) / 2730 / (main_post_pressure+0.1) / \
                              (selected_post_diameter / 18.8) / (selected_post_diameter / 18.8)
                 # 根据流速范围设置不同颜色
                velocity_text = f"{post_velocity:.2f}"

                # 设置颜色基于流速值
                if post_velocity <= 20:
                    self.main_post_velocity.configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                elif post_velocity <= 25:
                    self.main_post_velocity.configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                else:
                    self.main_post_velocity.configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
            else:
                self.main_post_velocity.configure(text="")
        except Exception:
            # 出错时不显示错误消息，保持静默
            self.main_post_velocity.configure(text="")
        # 【新增方法】
    def auto_calculate_bypass_pipe(self, *args):
        """自动计算支通路管径"""
        try:
            # 获取输入值
            try:
                bypass_flow = float(self.bypass_total_flow.get() or 0)
                edge_flow = float(self.edge_flow.get() or 0)  # 确保获取边火加热流量
                main_pressure = float(self.main_valve_pre.get() or 0.1)  # 默认值0.1MPa防止除零

                bypass_main_pre_pressure = float(self.bypass_main_valve_pre.get() or 0.1)  # 支通路总管阀前压力
                bypass_main_post_pressure = float(self.bypass_main_valve_post.get() or 0.1)  # 支通路总管阀后压力
                # 获取边火阀前压力和边火阀后压力
                branch_pre_pressure = float(self.bypass_vars['branch_pre']['pressure'].get() or 0.1)  # 边火阀前压力
                branch_post_pressure = float(self.bypass_vars['branch_post']['pressure'].get() or 0.1)  # 边火阀后压力
                temperature = float(self.temperature.get() or 0)
                main_velocity = float(self.main_velocity.get() or 1)  # 默认值1m/s防止除零


                # 计算支通路总管阀前管径
                # 修改公式: 18.8*sqrt(支通路总流量*（273+环境温度）/2730/（支通路总管阀前压力+0.1）/天然气总管流速)
                if bypass_flow > 0 and bypass_main_pre_pressure > 0 and main_velocity > 0:
                    bypass_main_pre_diameter = 18.8 * math.sqrt(
                        (bypass_flow * (273 + temperature) / 2730 / (bypass_main_pre_pressure+0.1))
                        / main_velocity
                    )
                    self.bypass_vars['main_pre']['calc'].configure(text=f"{bypass_main_pre_diameter:.2f}" if bypass_main_pre_diameter > 0 else "")

                # 计算支通路总管阀后管径
                # 修改公式: 18.8*sqrt(支通路总流量*（273+环境温度）/2730/（支通路总管阀后压力+0.1）/天然气总管流速)
                if bypass_flow > 0 and bypass_main_post_pressure > 0 and main_velocity > 0:
                    bypass_main_post_diameter = 18.8 * math.sqrt(
                        (bypass_flow * (273 + temperature) / 2730 / (bypass_main_post_pressure+0.1))
                        / main_velocity
                    )
                    self.bypass_vars['main_post']['calc'].configure(text=f"{bypass_main_post_diameter:.2f}" if bypass_main_post_diameter > 0 else "")
                # 计算边火支管阀前管径 - 修正公式
                if edge_flow > 0 and branch_pre_pressure > 0 and main_velocity > 0:
                    branch_pre_diameter = 18.8 * math.sqrt(
                        (edge_flow * (273 + temperature) / 2730 / (branch_pre_pressure+0.1))
                        / main_velocity
                    )
                    self.bypass_vars['branch_pre']['calc'].configure(text=f"{branch_pre_diameter:.2f}" if branch_pre_diameter > 0 else "")

                # 计算边火支管阀后管径 - 修正公式
                if edge_flow > 0 and branch_post_pressure > 0 and main_velocity > 0:
                    branch_post_diameter = 18.8 * math.sqrt(
                        (edge_flow * (273 + temperature) / 2730 / (branch_post_pressure+0.1))
                        / main_velocity
                    )
                    self.bypass_vars['branch_post']['calc'].configure(text=f"{branch_post_diameter:.2f}" if branch_post_diameter > 0 else "")
                # 计算反算流速
                selected_bypass_pre_diameter = float(self.bypass_vars['main_pre']['selected'].get() or 0)
                if selected_bypass_pre_diameter > 0 and main_pressure > 0:
                    # 修改反算总管阀前流速计算公式
                    bypass_pre_velocity = bypass_flow * (273 + temperature) / 2730 / (bypass_main_pre_pressure+0.1) / \
                                       (selected_bypass_pre_diameter / 18.8) / (selected_bypass_pre_diameter / 18.8)
                    self.bypass_vars['main_pre']['velocity'].configure(text=f"{bypass_pre_velocity:.2f}" if bypass_pre_velocity > 0 else "")

                selected_bypass_post_diameter = float(self.bypass_vars['main_post']['selected'].get() or 0)
                if selected_bypass_post_diameter > 0 and main_pressure > 0:
                    # 修改反算总管阀后流速计算公式
                    bypass_post_velocity = bypass_flow * (273 + temperature) / 2730 / (bypass_main_post_pressure+0.1) / \
                                        (selected_bypass_post_diameter / 18.8) / (selected_bypass_post_diameter / 18.8)
                    self.bypass_vars['main_post']['velocity'].configure(text=f"{bypass_post_velocity:.2f}" if bypass_post_velocity > 0 else "")

            except ValueError:
                pass

        except Exception as e:
            # 出错时不显示错误消息，保持静默
            pass
    # 【新增方法】- 添加在这里
    def auto_calculate_bypass_pre_velocity(self, *args):
        """自动计算支通路总管阀前反算流速"""
        try:
            bypass_flow = float(self.bypass_total_flow.get() or 0)
            bypass_main_pre_pressure = float(self.bypass_main_valve_pre.get() or 0.1)
            temperature = float(self.temperature.get() or 0)
            selected_bypass_pre_diameter = float(self.bypass_vars['main_pre']['selected'].get() or 0)

            if selected_bypass_pre_diameter > 0 and bypass_main_pre_pressure > 0:
                # 反算总管阀前流速=支通路总流量*（273+环境温度）/2730/（支通路总管阀前压力+0.1）/（支通路计算内容中的总管阀前管径选取/18.8）/（支通路计算内容中的总管阀前管径选取/18.8）
                bypass_pre_velocity = bypass_flow * (273 + temperature) / 2730 / (bypass_main_pre_pressure+0.1) / \
                                   (selected_bypass_pre_diameter / 18.8) / (selected_bypass_pre_diameter / 18.8)
                # 添加颜色标记
                velocity_text = f"{bypass_pre_velocity:.2f}"

                # 设置颜色基于流速值
                if bypass_pre_velocity <= 20:
                    self.bypass_vars['main_pre']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                elif bypass_pre_velocity <= 25:
                    self.bypass_vars['main_pre']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                else:
                    self.bypass_vars['main_pre']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
            else:
                self.bypass_vars['main_pre']['velocity'].configure(text="")
        except Exception:
            # 出错时不显示错误消息，保持静默
            self.bypass_vars['main_pre']['velocity'].configure(text="")

    # 【新增方法】
    def auto_calculate_bypass_post_velocity(self, *args):
        """自动计算支通路总管阀后反算流速"""
        try:
            bypass_flow = float(self.bypass_total_flow.get() or 0)
            bypass_main_post_pressure = float(self.bypass_main_valve_post.get() or 0.1)
            temperature = float(self.temperature.get() or 0)
            selected_bypass_post_diameter = float(self.bypass_vars['main_post']['selected'].get() or 0)

            if selected_bypass_post_diameter > 0 and bypass_main_post_pressure > 0:
                # 反算总管阀后流速=支通路总流量*（273+环境温度）/2730/（支通路总管阀后压力+0.1）/（支通路计算内容中的总管阀后管径选取/18.8）/（支通路计算内容中的总管阀后管径选取/18.8）
                bypass_post_velocity = bypass_flow * (273 + temperature) / 2730 / (bypass_main_post_pressure+0.1) / \
                                    (selected_bypass_post_diameter / 18.8) / (selected_bypass_post_diameter / 18.8)
                # 添加颜色标记
                velocity_text = f"{bypass_post_velocity:.2f}"

                # 设置颜色基于流速值
                if bypass_post_velocity <= 20:
                    self.bypass_vars['main_post']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                elif bypass_post_velocity <= 25:
                    self.bypass_vars['main_post']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                else:
                    self.bypass_vars['main_post']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
            else:
                self.bypass_vars['main_post']['velocity'].configure(text="")
        except Exception:
            # 出错时不显示错误消息，保持静默
            self.bypass_vars['main_post']['velocity'].configure(text="")
    # 【新增方法】- 边火支管阀前反算流速
    def auto_calculate_branch_pre_velocity(self, *args):
        """自动计算边火支管阀前反算流速"""
        try:
            edge_flow = float(self.edge_flow.get() or 0)  # 使用边火加热流量
            branch_pre_pressure = float(self.bypass_vars['branch_pre']['pressure'].get() or 0.1)  # 边火阀前压力
            temperature = float(self.temperature.get() or 0)
            selected_branch_pre_diameter = float(self.bypass_vars['branch_pre']['selected'].get() or 0)

            if selected_branch_pre_diameter > 0 and branch_pre_pressure > 0:
                # 反算边火支管阀前流速=边火加热流量*（273+环境温度）/2730/（边火阀前压力+0.1）/（边火支管阀前管径选取/18.8）/（边火支管阀前管径选取/18.8）
                branch_pre_velocity = edge_flow * (273 + temperature) / 2730 / (branch_pre_pressure+0.1) / \
                                   (selected_branch_pre_diameter / 18.8) / (selected_branch_pre_diameter / 18.8)
                # 添加颜色标记
                velocity_text = f"{branch_pre_velocity:.2f}"

                # 设置颜色基于流速值
                if branch_pre_velocity <= 20:
                    self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                elif branch_pre_velocity <= 25:
                    self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                else:
                    self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
            else:
                self.bypass_vars['branch_pre']['velocity'].configure(text="")
        except Exception:
            # 出错时不显示错误消息，保持静默
            self.bypass_vars['branch_pre']['velocity'].configure(text="")

    # 【新增方法】- 边火支管阀后反算流速
    def auto_calculate_branch_post_velocity(self, *args):
        """自动计算边火支管阀后反算流速"""
        try:
            edge_flow = float(self.edge_flow.get() or 0)  # 使用边火加热流量
            branch_post_pressure = float(self.bypass_vars['branch_post']['pressure'].get() or 0.1)  # 边火阀后压力
            temperature = float(self.temperature.get() or 0)
            selected_branch_post_diameter = float(self.bypass_vars['branch_post']['selected'].get() or 0)

            if selected_branch_post_diameter > 0 and branch_post_pressure > 0:
                # 反算边火支管阀后流速=边火加热流量*（273+环境温度）/2730/（边火阀后压力+0.1）/（边火支管阀后管径选取/18.8）/（边火支管阀后管径选取/18.8）
                branch_post_velocity = edge_flow * (273 + temperature) / 2730 / (branch_post_pressure+0.1) / \
                                    (selected_branch_post_diameter / 18.8) / (selected_branch_post_diameter / 18.8)
                # 添加颜色标记
                velocity_text = f"{branch_post_velocity:.2f}"

                # 设置颜色基于流速值
                if branch_post_velocity <= 20:
                    self.bypass_vars['branch_post']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                elif branch_post_velocity <= 25:
                    self.bypass_vars['branch_post']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                else:
                    self.bypass_vars['branch_post']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
            else:
                self.bypass_vars['branch_post']['velocity'].configure(text="")
        except Exception:
            # 出错时不显示错误消息，保持静默
            self.bypass_vars['branch_post']['velocity'].configure(text="")


    def create_input_section(self, parent):
        """保留此方法以兼容其他代码但不再使用"""
        pass

    def create_flow_section(self, parent):
        """保留此方法以兼容其他代码但不再使用"""
        pass

    def create_pressure_section(self, parent):
        """保留此方法以兼容其他代码但不再使用"""
        pass

    def create_diameter_section(self, parent):
        """保留此方法以兼容其他代码但不再使用"""
        pass

    def create_velocity_section(self, parent):
        """保留此方法以兼容其他代码但不再使用"""
        pass

    def create_furnace_section(self):
        """创建小炉区"""
        # 创建可伸缩的框架
        self.furnace_frame = ttk.LabelFrame(self.work_area, text="小炉区")
        # 添加到work_area，但不使用sticky参数
        self.work_area.add(self.furnace_frame)

        # 创建内部滚动显示区域
        self.furnace_inner_frame = ttk.Frame(self.furnace_frame)
        self.furnace_inner_frame.pack(fill="both", expand=True)

        # 初始化小炉显示
        self.update_furnace_display()

        # 绑定小炉数量变化事件
        self.furnace_count.trace_add("write", self.update_furnace_display)

    def update_furnace_display(self, *args):
        """更新小炉显示"""
        try:
            # 获取小炉数量
            try:
                furnace_count = int(self.furnace_count.get() or 1)
            except:
                furnace_count = 1

            if furnace_count <= 0:
                furnace_count = 1
            elif furnace_count > 50:
                furnace_count = 50

            # 清除现有的小炉数据
            for widget in self.furnace_inner_frame.winfo_children():
                widget.destroy()

            # 创建一个内部的可滚动区域 - 使用Canvas和滚动条来实现自适应显示
            # 这种方法可以保持界面体积不变，但内容可以滚动显示
            canvas = tk.Canvas(self.furnace_inner_frame)
            scrollbar = ttk.Scrollbar(self.furnace_inner_frame, orient="vertical", command=canvas.yview)
            scroll_frame = ttk.Frame(canvas)

            # 配置滚动区域
            scroll_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scroll_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # 创建表头
            headers = ["小炉编号", "平均热负荷(%)", "浮动值(%)", "喷枪数", "最大流量",
                    "正常流量", "最小流量", "计算阀前管径", "选取阀前管径", "反算阀前流速(m/s)",
                    "计算阀后管径", "选取阀后管径", "反算阀后流速"]

            for col, header in enumerate(headers):
                ttk.Label(scroll_frame, text=header).grid(row=0, column=col, padx=5, pady=2)

            # 创建小炉行
            self.furnace_data = []
            for i in range(furnace_count):
                furnace = self.create_furnace_row(scroll_frame, i+1)
                self.furnace_data.append(furnace)

            # 布局滚动组件
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")
             # 更新设备表中的设备4，与小炉数量联动
            if hasattr(self, 'equipment_manager'):
                self.equipment_manager.update_equipment4_with_furnace_count(furnace_count)

        except Exception as e:
            messagebox.showerror("错误", f"更新小炉显示时出错: {str(e)}")

    def adjust_furnace_canvas(self, event=None):
        """调整小炉区画布大小和滚动区域"""
        self.furnace_canvas.configure(scrollregion=self.furnace_canvas.bbox("all"))

        # 根据小炉数量动态调整画布高度
        furnace_count = len(self.furnace_data)
        if furnace_count <= 0:
            furnace_count = 1

        # 计算适当的高度 (表头 + 每行高度 × 小炉数量)
        # 表头行高大约为30像素，每个小炉行约为35像素
        header_height = 30
        row_height = 35
        total_height = header_height + row_height * furnace_count

        # 设置最小高度，确保至少显示表头和一行
        min_height = header_height + row_height
        # 设置最大高度，避免过大（例如最多显示10行，超过则使用滚动条）
        max_visible_rows = 10
        max_height = header_height + row_height * min(furnace_count, max_visible_rows)

        # 如果小炉数量超过最大可见行数，使用最大高度并启用滚动
        if furnace_count > max_visible_rows:
            self.furnace_canvas.configure(height=max_height)
        else:
            # 否则，根据实际数量设置高度
            self.furnace_canvas.configure(height=total_height)

    def adjust_furnace_height(self, furnace_count):
        """根据小炉数量调整画布高度"""
        if not hasattr(self, 'furnace_canvas'):
            return

        # 表头行高和每行高度
        header_height = 30
        row_height = 35

        # 如果是初始状态（只有1个小炉），只显示表头和一行
        if furnace_count == 1:
            initial_height = header_height + row_height
            self.furnace_canvas.configure(height=initial_height)
        else:
            # 否则根据小炉数量调整高度，但不超过最大可见行数
            max_visible_rows = 10
            display_rows = min(furnace_count, max_visible_rows)
            total_height = header_height + row_height * display_rows
            self.furnace_canvas.configure(height=total_height)
    def create_furnace_row(self, scrollable_frame, furnace_num):
        """创建单个小炉行"""
        row = furnace_num
        furnace = {}

        # 小炉编号
        furnace['number'] = ttk.Label(scrollable_frame, text=f"小炉{furnace_num}")
        furnace['number'].grid(row=row, column=0, padx=5, pady=2)

        # 平均热负荷
        furnace['heat_load'] = tk.StringVar(value="")
        entry = ttk.Entry(scrollable_frame, textvariable=furnace['heat_load'], width=10)
        entry.grid(row=row, column=1, padx=5, pady=2)

        # 浮动值
        furnace['float_value'] = tk.StringVar(value="")
        entry = ttk.Entry(scrollable_frame, textvariable=furnace['float_value'], width=10)
        entry.grid(row=row, column=2, padx=5, pady=2)

        # 喷枪数
        furnace['nozzle_count'] = tk.StringVar(value="")
        entry = ttk.Entry(scrollable_frame, textvariable=furnace['nozzle_count'], width=10)
        entry.grid(row=row, column=3, padx=5, pady=2)

        # 计算结果显示标签
        for i, name in enumerate(['max_flow', 'normal_flow', 'min_flow', 'calc_diameter']):
            furnace[name] = ttk.Label(scrollable_frame, text="")
            furnace[name].grid(row=row, column=4+i, padx=5, pady=2)

        # 选取阀前管径
        furnace['selected_diameter'] = tk.StringVar(value="")
        entry = ttk.Entry(scrollable_frame, textvariable=furnace['selected_diameter'], width=10)
        entry.grid(row=row, column=8, padx=5, pady=2)

        # 反算阀前流速
        furnace['calc_velocity'] = ttk.Label(scrollable_frame, text="")
        furnace['calc_velocity'].grid(row=row, column=9, padx=5, pady=2)
        # 添加以下三个新字段 - 修改这里
        # 计算阀后管径
        furnace['calc_post_diameter'] = ttk.Label(scrollable_frame, text="")
        furnace['calc_post_diameter'].grid(row=row, column=10, padx=5, pady=2)  # <-- 新增

        # 选取阀后管径
        furnace['selected_post_diameter'] = tk.StringVar(value="")
        entry = ttk.Entry(scrollable_frame, textvariable=furnace['selected_post_diameter'], width=10)
        entry.grid(row=row, column=11, padx=5, pady=2)  # <-- 新增

        # 反算阀后流速
        furnace['calc_post_velocity'] = ttk.Label(scrollable_frame, text="")
        furnace['calc_post_velocity'].grid(row=row, column=12, padx=5, pady=2)  # <-- 新增

        # 绑定计算事件
        for var in [furnace['heat_load'], furnace['float_value'],
                    furnace['nozzle_count'], furnace['selected_diameter'],
                    furnace['selected_post_diameter']]:  # <-- 添加新变量
            var.trace_add("write", lambda *args: self.calculate_furnace_values())

        # 【新增】绑定平均热负荷和浮动值变化事件，触发调节阀分组更新
        furnace['heat_load'].trace_add("write", lambda *args: self.update_valve_calculator_grouping())
        furnace['float_value'].trace_add("write", lambda *args: self.update_valve_calculator_grouping())

        # 特别处理喷枪数变化事件，添加同步到小炉阀门区域的功能
        trace_id = furnace['nozzle_count'].trace_add("write", lambda *args, idx=furnace_num-1:
                                                    self.sync_nozzle_count_to_valve(idx))
        furnace['nozzle_count_trace_id'] = trace_id
        return furnace
     # 新增方法：同步喷枪数到小炉阀门区域
    def sync_nozzle_count_to_valve(self, index):
        """同步主界面的喷枪数到小炉阀门区域"""
        try:
            # 先执行原有的计算
            self.calculate_furnace_values()

            # 如果小炉阀门数据已初始化且索引有效
            if hasattr(self, 'small_valve_data') and self.small_valve_data and index < len(self.small_valve_data):
                # 获取主界面的喷枪数
                nozzle_count = self.furnace_data[index]['nozzle_count'].get()

                # 更新小炉阀门区域的喷枪数，避免循环调用
                if nozzle_count and nozzle_count != self.small_valve_data[index]['nozzle_count'].get():
                    # 暂时解除小炉阀门区域喷枪数的跟踪
                    self.small_valve_data[index]['nozzle_count'].trace_remove("write",
                        self.small_valve_data[index].get('nozzle_count_trace_id', None))

                    # 更新小炉阀门区域喷枪数
                    self.small_valve_data[index]['nozzle_count'].set(nozzle_count)

                    # 重新绑定小炉阀门区域喷枪数的跟踪
                    trace_id = self.small_valve_data[index]['nozzle_count'].trace_add("write",
                        lambda *args, idx=index: self.sync_nozzle_count_to_main(idx))
                    self.small_valve_data[index]['nozzle_count_trace_id'] = trace_id
        except Exception as e:
            # 出错时不显示错误消息，保持静默
            pass

        # 如果调节阀计算窗口已打开，触发分组更新
        if hasattr(self, 'valve_calculator') and hasattr(self.valve_calculator, 'small_valve_data'):
            try:
                self.valve_calculator.update_small_valve_display()
            except Exception as e:
                print(f"更新调节阀分组显示时出错: {str(e)}")

    def update_valve_calculator_grouping(self):
        """当主界面平均热负荷或浮动值变化时，更新调节阀计算界面的分组"""
        try:
            # 检查调节阀计算窗口是否已打开并且界面元素已创建
            if (hasattr(self, 'valve_calculator') and
                hasattr(self.valve_calculator, 'update_small_valve_display') and
                hasattr(self.valve_calculator, 'small_valve_scrollable_frame')):
                print("检测到平均热负荷或浮动值变化，正在更新调节阀分组...")
                self.valve_calculator.update_small_valve_display()
                print("调节阀分组更新完成")
            else:
                print("调节阀计算窗口未打开或界面未完全初始化，跳过分组更新")
        except Exception as e:
            print(f"更新调节阀分组时出错: {str(e)}")

    def create_output_section(self):
        """创建输出区"""
        output_frame = ttk.LabelFrame(self.work_area, text="输出区")
        self.work_area.add(output_frame)

        # 创建颜色标记说明区域
        color_legend_frame = ttk.Frame(output_frame)
        color_legend_frame.pack(fill=tk.X, padx=5, pady=2)
        ttk.Label(color_legend_frame, text="颜色标记说明: ", font=("宋体", 9, "bold")).pack(side=tk.LEFT, padx=5)
        ttk.Label(color_legend_frame, text="计算值", foreground="#0066cc").pack(side=tk.LEFT, padx=5)
        ttk.Label(color_legend_frame, text="正常范围", foreground="#009900").pack(side=tk.LEFT, padx=5)
        ttk.Label(color_legend_frame, text="注意范围", foreground="#ff9900").pack(side=tk.LEFT, padx=5)
        ttk.Label(color_legend_frame, text="异常范围", foreground="#cc0000").pack(side=tk.LEFT, padx=5)

        # 创建总管计算内容
        main_calc_frame = ttk.LabelFrame(output_frame, text="总管计算内容")
        main_calc_frame.pack(fill=tk.X, padx=5, pady=5)

        # 总管计算内容字段
        self.main_pre_calc_diameter = ttk.Label(main_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold"))  # 总管调节阀前管径，使用蓝色
        self.main_pre_selected = tk.StringVar()  # 总管阀前管径选取
        self.main_pre_velocity = ttk.Label(main_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold"))  # 反算总管阀前流速，使用蓝色
        self.main_post_calc_diameter = ttk.Label(main_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold"))  # 总管调节阀后管径，使用蓝色
        self.main_post_selected = tk.StringVar()  # 总管阀后管径选取
        self.main_post_velocity = ttk.Label(main_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold"))  # 反算总管阀后流速，使用蓝色

        # 放置总管计算内容控件
        row = 0
        ttk.Label(main_calc_frame, text="总管调节阀前管径:").grid(row=row, column=0, padx=5, pady=2)
        self.main_pre_calc_diameter.grid(row=row, column=1, padx=5, pady=2)
        ttk.Label(main_calc_frame, text="总管阀前管径选取:").grid(row=row, column=2, padx=5, pady=2)
        ttk.Entry(main_calc_frame, textvariable=self.main_pre_selected).grid(row=row, column=3, padx=5, pady=2)
        # 绑定变量跟踪，当self.main_pre_selected变化时同步回self.main_pre_diameter
        self.main_pre_selected.trace_add("write", self.sync_main_pre_selected)
        ttk.Label(main_calc_frame, text="反算总管阀前流速:").grid(row=row, column=4, padx=5, pady=2)
        self.main_pre_velocity.grid(row=row, column=5, padx=5, pady=2)

        row = 1
        ttk.Label(main_calc_frame, text="总管调节阀后管径:").grid(row=row, column=0, padx=5, pady=2)
        self.main_post_calc_diameter.grid(row=row, column=1, padx=5, pady=2)
        ttk.Label(main_calc_frame, text="总管阀后管径选取:").grid(row=row, column=2, padx=5, pady=2)
        ttk.Entry(main_calc_frame, textvariable=self.main_post_selected).grid(row=row, column=3, padx=5, pady=2)
        # 绑定变量跟踪，当self.main_post_selected变化时同步回self.main_post_diameter
        self.main_post_selected.trace_add("write", self.sync_main_post_selected)
        ttk.Label(main_calc_frame, text="反算总管阀后流速:").grid(row=row, column=4, padx=5, pady=2)
        self.main_post_velocity.grid(row=row, column=5, padx=5, pady=2)

        # 创建支通路计算内容
        bypass_calc_frame = ttk.LabelFrame(output_frame, text="支通路计算内容")
        bypass_calc_frame.pack(fill=tk.X, padx=5, pady=5)

        # 支通路计算内容字段 - 为计算结果添加颜色
        self.bypass_vars = {
            'main_pre': {'calc': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold")),
                        'selected': tk.StringVar(),
                        'velocity': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold"))},
            'main_post': {'calc': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold")),
                        'selected': tk.StringVar(),
                        'velocity': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold"))},
            'branch_pre': {'calc': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold")),
                        'selected': tk.StringVar(),
                        'velocity': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold")),
                        'pressure': tk.StringVar()},  # 添加边火阀前压力变量
            'branch_post': {'calc': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold")),
                        'selected': tk.StringVar(),
                        'velocity': ttk.Label(bypass_calc_frame, text="", foreground="#0066cc", font=("宋体", 9, "bold")),
                        'pressure': tk.StringVar()}  # 添加边火阀后压力变量
        }

        # 放置支通路计算内容控件
        labels = [
            ("总管阀前管径:", "总管阀前管径选取:", "反算总管阀前流速:"),
            ("总管阀后管径:", "总管阀后管径选取:", "反算总管阀后流速:"),
            ("边火支管阀前管径:", "支管阀前管径选取:", "反算支管阀前流速:", "边火阀前压力(MPa):"),  # 添加边火阀前压力标签
            ("边火支管阀后管径:", "支管阀后管径选取:", "反算支管阀后流速:", "边火阀后压力(MPa):")   # 添加边火阀后压力标签
        ]

        for row, (key, label_group) in enumerate(zip(self.bypass_vars.keys(), labels)):
            ttk.Label(bypass_calc_frame, text=label_group[0]).grid(row=row, column=0, padx=5, pady=2)
            self.bypass_vars[key]['calc'].grid(row=row, column=1, padx=5, pady=2)
            ttk.Label(bypass_calc_frame, text=label_group[1]).grid(row=row, column=2, padx=5, pady=2)
            ttk.Entry(bypass_calc_frame, textvariable=self.bypass_vars[key]['selected']).grid(row=row, column=3, padx=5, pady=2)
            ttk.Label(bypass_calc_frame, text=label_group[2]).grid(row=row, column=4, padx=5, pady=2)
            self.bypass_vars[key]['velocity'].grid(row=row, column=5, padx=5, pady=2)
            # 为边火支管添加压力输入框
            if key in ['branch_pre', 'branch_post']:
                ttk.Label(bypass_calc_frame, text=label_group[3]).grid(row=row, column=6, padx=5, pady=2)
                ttk.Entry(bypass_calc_frame, textvariable=self.bypass_vars[key]['pressure']).grid(row=row, column=7, padx=5, pady=2)
                # 添加变量跟踪，当压力变化时重新计算
                self.bypass_vars[key]['pressure'].trace_add("write", self.auto_calculate_bypass_pipe)
            # 为支通路总管阀前和总管阀后添加变量跟踪
            if key == 'main_pre':
                self.bypass_vars[key]['selected'].trace_add("write", self.sync_bypass_main_pre_selected)
                # 添加变量跟踪，当支通路总管阀前管径选取变化时自动计算反算流速
                self.bypass_vars[key]['selected'].trace_add("write", self.auto_calculate_bypass_pre_velocity)
            elif key == 'main_post':
                self.bypass_vars[key]['selected'].trace_add("write", self.sync_bypass_main_post_selected)
                # 添加变量跟踪，当支通路总管阀后管径选取变化时自动计算反算流速
                self.bypass_vars[key]['selected'].trace_add("write", self.auto_calculate_bypass_post_velocity)
            # 为边火支管添加变量跟踪，当管径选取变化时自动计算反算流速
            elif key == 'branch_pre':
                self.bypass_vars[key]['selected'].trace_add("write", self.auto_calculate_branch_pre_velocity)
            elif key == 'branch_post':
                self.bypass_vars[key]['selected'].trace_add("write", self.auto_calculate_branch_post_velocity)

    def calculate_all(self):
        """计算所有值"""
        try:
            # 计算小炉区的值
            self.calculate_furnace_values()

            # 计算总管和支通路的值
            # 获取输入值
            try:
                old_flow = float(self.old_flow.get() or 0)
                normal_flow = float(self.normal_flow.get() or 0)
                main_pressure = float(self.main_valve_pre.get() or 0.1)  # 默认值0.1MPa防止除零
                temperature = float(self.temperature.get() or 0)
                main_velocity = float(self.main_velocity.get() or 1)  # 默认值1m/s防止除零

                # 计算总管调节阀前管径
                if old_flow > 0 and main_pressure > 0 and main_velocity > 0:
                    main_pre_diameter = 18.8 * math.sqrt(
                        (old_flow * (273 + temperature) / 2730 / main_pressure)
                        / main_velocity
                    )
                    self.main_pre_calc_diameter.configure(text=f"{main_pre_diameter:.2f}" if main_pre_diameter > 0 else "")

                # 计算总管调节阀后管径
                if normal_flow > 0 and main_pressure > 0 and main_velocity > 0:
                    main_post_diameter = 18.8 * math.sqrt(
                        (normal_flow * (273 + temperature) / 2730 / main_pressure)
                        / main_velocity
                    )
                    self.main_post_calc_diameter.configure(text=f"{main_post_diameter:.2f}" if main_post_diameter > 0 else "")

                # 计算反算流速
                selected_pre_diameter = float(self.main_pre_selected.get() or 0)
                if selected_pre_diameter > 0 and main_pressure > 0:
                    pre_velocity = (old_flow * (273 + temperature) / 2730 / main_pressure) / \
                                 ((selected_pre_diameter / 18.8) * (selected_pre_diameter / 18.8))
                    self.main_pre_velocity.configure(text=f"{pre_velocity:.2f}" if pre_velocity > 0 else "")

                selected_post_diameter = float(self.main_post_selected.get() or 0)
                if selected_post_diameter > 0 and main_pressure > 0:
                    post_velocity = (normal_flow * (273 + temperature) / 2730 / main_pressure) / \
                                  ((selected_post_diameter / 18.8) * (selected_post_diameter / 18.8))
                    self.main_post_velocity.configure(text=f"{post_velocity:.2f}" if post_velocity > 0 else "")

            except ValueError:
                pass

        except Exception as e:
            messagebox.showerror("错误", f"计算时出错: {str(e)}")
    def show_valve_calculator(self):
        """显示阀门计算窗口"""
        # 调用ValveCalculator类中的show_valve_calculator方法
        self.valve_calculator.show_valve_calculator()

    def calculate_valve_diameter(self, flow, pressure):
        """计算阀门口径
        Args:
            flow: 流量(Nm³/h)
            pressure: 压力(MPa)
        Returns:
            diameter: 计算口径(mm)
        """
        # 这里使用简化的计算公式，实际应用中需要根据具体要求调整
        diameter = math.sqrt((4 * flow) / (3.14 * 3600 * 20 * math.sqrt(pressure * 1e6)))  # 20m/s为假设流速
        return diameter * 1000  # 转换为mm

 
    def fill_data_from_dict(self, data_dict):
        """从字典填充数据到界面"""
        # 清空当前数据
        self.clear_form_data()
        
        # 基本项目信息
        if "工程名称" in data_dict:
            self.project_name.set(data_dict["工程名称"])
        if "工程代号" in data_dict:
            self.project_code.set(data_dict["工程代号"])
        if "项目类型" in data_dict:
            self.project_type.set(data_dict["项目类型"])
        if "项目地址" in data_dict:
            self.project_location.set(data_dict["项目地址"])
        if "产能(t/d)" in data_dict:
            self.capacity.set(data_dict["产能(t/d)"])
        if "小炉数" in data_dict:
            self.furnace_count.set(data_dict["小炉数"])
        if "一窑几线" in data_dict:
            self.line_count.set(data_dict["一窑几线"])
        
        # 流量数据
        if "安全阀流量(Nm³/h)" in data_dict:
            self.safety_valve_flow.set(data_dict["安全阀流量(Nm³/h)"])
        if "正常生产流量(Nm³/h)" in data_dict:
            self.normal_flow.set(data_dict["正常生产流量(Nm³/h)"])
        if "交通路点流量(Nm³/h)" in data_dict:
            self.crossing_flow.set(data_dict["交通路点流量(Nm³/h)"])
        if "成型窑加热流量(Nm³/h)" in data_dict:
            self.forming_flow.set(data_dict["成型窑加热流量(Nm³/h)"])
        if "边火加热流量(Nm³/h)" in data_dict:
            self.edge_heating_flow.set(data_dict["边火加热流量(Nm³/h)"])
        
        # 压力数据
        if "进车间压力(MPa)" in data_dict:
            self.inlet_pressure.set(data_dict["进车间压力(MPa)"])
        if "总管调节阀前压力(MPa)" in data_dict:
            self.main_pre_pressure.set(data_dict["总管调节阀前压力(MPa)"])
        if "总管调节阀后压力(MPa)" in data_dict:
            self.main_post_pressure.set(data_dict["总管调节阀后压力(MPa)"])
        if "小炉支管调节阀前压力(MPa)" in data_dict:
            self.branch_pre_pressure.set(data_dict["小炉支管调节阀前压力(MPa)"])
        if "小炉支管调节阀后压力(MPa)" in data_dict:
            self.branch_post_pressure.set(data_dict["小炉支管调节阀后压力(MPa)"])
        if "支通路总管阀前压力(MPa)" in data_dict:
            self.bypass_main_pre_pressure.set(data_dict["支通路总管阀前压力(MPa)"])
        if "支通路总管阀后压力(MPa)" in data_dict:
            self.bypass_main_post_pressure.set(data_dict["支通路总管阀后压力(MPa)"])
        
        # 管径数据
        if "总管阀前计算管径(mm)" in data_dict:
            self.main_pre_diameter.set(data_dict["总管阀前计算管径(mm)"])
        if "总管阀前选定管径(mm)" in data_dict:
            self.main_pre_selected_diameter.set(data_dict["总管阀前选定管径(mm)"])
        if "总管阀后计算管径(mm)" in data_dict:
            self.main_post_diameter.set(data_dict["总管阀后计算管径(mm)"])
        if "总管阀后选定管径(mm)" in data_dict:
            self.main_post_selected_diameter.set(data_dict["总管阀后选定管径(mm)"])
        if "支通路总管阀前计算管径(mm)" in data_dict:
            self.bypass_main_pre_diameter.set(data_dict["支通路总管阀前计算管径(mm)"])
        if "支通路总管阀前选定管径(mm)" in data_dict:
            self.bypass_main_pre_selected_diameter.set(data_dict["支通路总管阀前选定管径(mm)"])
        if "支通路总管阀后计算管径(mm)" in data_dict:
            self.bypass_main_post_diameter.set(data_dict["支通路总管阀后计算管径(mm)"])
        if "支通路总管阀后选定管径(mm)" in data_dict:
            self.bypass_main_post_selected_diameter.set(data_dict["支通路总管阀后选定管径(mm)"])
        
        # 流速和温度数据
        if "天然气总管流速(m/s)" in data_dict:
            self.main_velocity.set(data_dict["天然气总管流速(m/s)"])
        if "小炉支管流速(m/s)" in data_dict:
            self.branch_velocity.set(data_dict["小炉支管流速(m/s)"])
        if "设计温度(℃)" in data_dict:
            self.design_temperature.set(data_dict["设计温度(℃)"])
        
        # 总管阀前反算流速和总管阀后反算流速
        if "总管阀前反算流速(m/s)" in data_dict:
            self.main_pre_actual_velocity.set(data_dict["总管阀前反算流速(m/s)"])
        if "总管阀后反算流速(m/s)" in data_dict:
            self.main_post_actual_velocity.set(data_dict["总管阀后反算流速(m/s)"])
        
        # 支通路反算流速
        if "支管阀前反算流速(m/s)" in data_dict:
            self.branch_pre_actual_velocity.set(data_dict["支管阀前反算流速(m/s)"])
        if "支管阀后反算流速(m/s)" in data_dict:
            self.branch_post_actual_velocity.set(data_dict["支管阀后反算流速(m/s)"])
        if "支通路总管阀前反算流速(m/s)" in data_dict:
            self.bypass_main_pre_actual_velocity.set(data_dict["支通路总管阀前反算流速(m/s)"])
        if "支通路总管阀后反算流速(m/s)" in data_dict:
            self.bypass_main_post_actual_velocity.set(data_dict["支通路总管阀后反算流速(m/s)"])
        
        # 阀门相关数据
        if "主调节阀" in data_dict:
            if hasattr(self, 'main_valve_c_selected_value'):
                self.main_valve_c_selected_value.set(data_dict["主调节阀"])
        if "总管C计大" in data_dict:
            if hasattr(self, 'main_valve_c_large'):
                self.main_valve_c_large.set(data_dict["总管C计大"])
        if "总管C计小" in data_dict:
            if hasattr(self, 'main_valve_c_small'):
                self.main_valve_c_small.set(data_dict["总管C计小"])
        if "总管C选定" in data_dict:
            if hasattr(self, 'main_valve_c_selected'):
                self.main_valve_c_selected.set(data_dict["总管C选定"])
        if "总管K大" in data_dict:
            if hasattr(self, 'main_valve_k_large'):
                self.main_valve_k_large.set(data_dict["总管K大"])
        if "总管K小" in data_dict:
            if hasattr(self, 'main_valve_k_small'):
                self.main_valve_k_small.set(data_dict["总管K小"])
        if "主阀C计大" in data_dict:
            if hasattr(self, 'main_valve_c_large'):
                self.main_valve_c_large.set(data_dict["主阀C计大"])
        if "主阀C计小" in data_dict:
            if hasattr(self, 'main_valve_c_small'):
                self.main_valve_c_small.set(data_dict["主阀C计小"])
        if "主阀C选定" in data_dict:
            if hasattr(self, 'main_valve_c_selected'):
                self.main_valve_c_selected.set(data_dict["主阀C选定"])
        if "主阀K大" in data_dict:
            if hasattr(self, 'main_valve_k_large'):
                self.main_valve_k_large.set(data_dict["主阀K大"])
        if "主阀K小" in data_dict:
            if hasattr(self, 'main_valve_k_small'):
                self.main_valve_k_small.set(data_dict["主阀K小"])
        
        # 安全阀相关数据
        if "安全阀公称通径" in data_dict:
            if hasattr(self, 'release_valve_dn'):
                self.release_valve_dn.set(data_dict["安全阀公称通径"])
        if "安全阀流通面积" in data_dict:
            if hasattr(self, 'release_valve_area'):
                self.release_valve_area.set(data_dict["安全阀流通面积"])
        if "安全阀喉径" in data_dict:
            if hasattr(self, 'release_valve_d0'):
                self.release_valve_d0.set(data_dict["安全阀喉径"])
        
        # 填充小炉数据
        if "小炉区数据" in data_dict and isinstance(data_dict["小炉区数据"], list):
            self.load_furnace_data(data_dict["小炉区数据"])
        
        # 氧气窑相关数据
        if "是否是全氧窑" in data_dict:
            if hasattr(self, 'is_oxygen_kiln'):
                self.is_oxygen_kiln.set(data_dict["是否是全氧窑"])
        if "是否有0#氧枪" in data_dict:
            if hasattr(self, 'has_oxygen_lance'):
                self.has_oxygen_lance.set(data_dict["是否有0#氧枪"])
        
        # 氧气管道数据
        if "氧气正常流量(Nm³/h)" in data_dict and hasattr(self, 'oxygen_flow'):
            self.oxygen_flow.set(data_dict["氧气正常流量(Nm³/h)"])
        if "窑老期流量(Nm³/h)" in data_dict and hasattr(self, 'oxygen_old_flow'):
            self.oxygen_old_flow.set(data_dict["窑老期流量(Nm³/h)"])
        if "氧气进车间压力(MPa)" in data_dict and hasattr(self, 'oxygen_inlet_pressure'):
            self.oxygen_inlet_pressure.set(data_dict["氧气进车间压力(MPa)"])
        if "氧气设计流速(m/s)" in data_dict and hasattr(self, 'oxygen_velocity'):
            self.oxygen_velocity.set(data_dict["氧气设计流速(m/s)"])
        
        # 填充压缩空气数据到内存，供后续使用
        if hasattr(self, 'compressed_air_calculator'):
            air_data = {}
            for key, value in data_dict.items():
                if (key.startswith("压缩空气") or 
                    key in ["进车间压力", "设计流速", "连续用气总量", "间歇用气总量", 
                            "总用气量", "计算管径", "选取管径", "实际流速"] or
                    key.startswith("蓄热室吹扫用气") or key.startswith("喷枪冷却用气") or
                    key.startswith("投料机用气") or key.startswith("阀及工业电视用气") or
                    key.startswith("冷端机组用气") or key.startswith("退火窑及红外用气") or
                    key.startswith("支通路加热用气") or key.startswith("压延机烧边火用气") or
                    key.startswith("压延机清理用气")):
                    air_data[key] = value
            
            # 如果有压缩空气数据，保存到内存中
            if air_data:
                if not hasattr(self, 'saved_air_data'):
                    self.saved_air_data = {}
                
                project_id = f"{self.project_name.get()}_{self.project_code.get()}"
                self.saved_air_data[project_id] = air_data
                print(f"已保存压缩空气数据到内存，项目ID: {project_id}")
        
        # 自力式阀数据
        self_operated_valve_data = {}
        for key, value in data_dict.items():
            if key.startswith("自力式阀"):
                self_operated_valve_data[key] = value
        
        if self_operated_valve_data and hasattr(self, 'self_operated_valve'):
            self.self_operated_valve.load_data(self_operated_valve_data)
            print("已加载自力式阀数据")
        
        # 氧枪数据
        if "氧枪氧气数据" in data_dict and hasattr(self, 'oxygen_lance_calculator'):
            self.oxygen_lance_calculator.load_oxygen_data(data_dict["氧枪氧气数据"])
            print("已加载氧枪氧气数据")
        if "氧枪天然气数据" in data_dict and hasattr(self, 'oxygen_lance_calculator'):
            self.oxygen_lance_calculator.load_natural_gas_data(data_dict["氧枪天然气数据"])
            print("已加载氧枪天然气数据")
        
        # 如果存在设备数据，加载设备数据
        if "设备数据" in data_dict and isinstance(data_dict["设备数据"], list):
            self.load_equipment_data(data_dict["设备数据"])
        
        # 重新计算所有值
        try:
            self.refresh_all_calculations()
        except Exception as e:
            print(f"刷新计算时出错: {str(e)}")
            traceback.print_exc()
        
        # 刷新显示
        self.update()
        print("数据填充完成")
        # 确保加载项目后更新设备表序号
        if "小炉数" in data_dict and hasattr(self, 'equipment_manager'):
            try:
                furnace_count = int(self.furnace_count.get() or 1)
                self.equipment_manager.update_equipment4_with_furnace_count(furnace_count)
            except Exception as e:
                print(f"首次加载项目时更新设备序号出错: {str(e)}")

    def clear_form_data(self):
        """清空表单中的所有数据"""
        # 基本项目信息
        if hasattr(self, 'project_name'):
            self.project_name.set("")
        if hasattr(self, 'project_code'):
            self.project_code.set("")
        if hasattr(self, 'project_type'):
            self.project_type.set("")
        if hasattr(self, 'project_location'):
            self.project_location.set("")
        if hasattr(self, 'capacity'):
            self.capacity.set("")
        if hasattr(self, 'furnace_count'):
            self.furnace_count.set("")
        if hasattr(self, 'line_count'):
            self.line_count.set("")
        
        # 流量数据
        if hasattr(self, 'safety_valve_flow'):
            self.safety_valve_flow.set("")
        if hasattr(self, 'normal_flow'):
            self.normal_flow.set("")
        if hasattr(self, 'crossing_flow'):
            self.crossing_flow.set("")
        if hasattr(self, 'forming_flow'):
            self.forming_flow.set("")
        if hasattr(self, 'edge_heating_flow'):
            self.edge_heating_flow.set("")
        
        # 压力数据
        if hasattr(self, 'inlet_pressure'):
            self.inlet_pressure.set("")
        if hasattr(self, 'main_pre_pressure'):
            self.main_pre_pressure.set("")
        if hasattr(self, 'main_post_pressure'):
            self.main_post_pressure.set("")
        if hasattr(self, 'branch_pre_pressure'):
            self.branch_pre_pressure.set("")
        if hasattr(self, 'branch_post_pressure'):
            self.branch_post_pressure.set("")
        if hasattr(self, 'bypass_main_pre_pressure'):
            self.bypass_main_pre_pressure.set("")
        if hasattr(self, 'bypass_main_post_pressure'):
            self.bypass_main_post_pressure.set("")
        
        # 管径数据
        if hasattr(self, 'main_pre_diameter'):
            self.main_pre_diameter.set("")
        if hasattr(self, 'main_pre_selected_diameter'):
            self.main_pre_selected_diameter.set("")
        if hasattr(self, 'main_post_diameter'):
            self.main_post_diameter.set("")
        if hasattr(self, 'main_post_selected_diameter'):
            self.main_post_selected_diameter.set("")
        if hasattr(self, 'bypass_main_pre_diameter'):
            self.bypass_main_pre_diameter.set("")
        if hasattr(self, 'bypass_main_pre_selected_diameter'):
            self.bypass_main_pre_selected_diameter.set("")
        if hasattr(self, 'bypass_main_post_diameter'):
            self.bypass_main_post_diameter.set("")
        if hasattr(self, 'bypass_main_post_selected_diameter'):
            self.bypass_main_post_selected_diameter.set("")
        
        # 流速和温度数据
        if hasattr(self, 'main_velocity'):
            self.main_velocity.set("")
        if hasattr(self, 'branch_velocity'):
            self.branch_velocity.set("")
        if hasattr(self, 'design_temperature'):
            self.design_temperature.set("")
        
        # 反算流速
        if hasattr(self, 'main_pre_actual_velocity'):
            self.main_pre_actual_velocity.set("")
        if hasattr(self, 'main_post_actual_velocity'):
            self.main_post_actual_velocity.set("")
        if hasattr(self, 'branch_pre_actual_velocity'):
            self.branch_pre_actual_velocity.set("")
        if hasattr(self, 'branch_post_actual_velocity'):
            self.branch_post_actual_velocity.set("")
        if hasattr(self, 'bypass_main_pre_actual_velocity'):
            self.bypass_main_pre_actual_velocity.set("")
        if hasattr(self, 'bypass_main_post_actual_velocity'):
            self.bypass_main_post_actual_velocity.set("")
        
        # 阀门数据
        if hasattr(self, 'main_valve_c_selected_value'):
            self.main_valve_c_selected_value.set("")
        if hasattr(self, 'main_valve_k_max_value'):
            self.main_valve_k_max_value.set("")
        if hasattr(self, 'main_valve_k_min_value'):
            self.main_valve_k_min_value.set("")
        if hasattr(self, 'release_valve_dn_value'):
            self.release_valve_dn_value.set("")
        if hasattr(self, 'release_valve_area_value'):
            self.release_valve_area_value.set("")
        if hasattr(self, 'release_valve_d0_value'):
            self.release_valve_d0_value.set("")
        
        # 清空主阀参数
        if hasattr(self, 'main_valve_c_large'):
            self.main_valve_c_large.set("")
        if hasattr(self, 'main_valve_c_small'):
            self.main_valve_c_small.set("")
        if hasattr(self, 'main_valve_c_selected'):
            self.main_valve_c_selected.set("")
        if hasattr(self, 'main_valve_k_large'):
            self.main_valve_k_large.set("")
        if hasattr(self, 'main_valve_k_small'):
            self.main_valve_k_small.set("")
        
        # 清空安全阀参数
        if hasattr(self, 'release_valve_dn'):
            self.release_valve_dn.set("")
        if hasattr(self, 'release_valve_area'):
            self.release_valve_area.set("")
        if hasattr(self, 'release_valve_d0'):
            self.release_valve_d0.set("")
        
        # 氧气窑相关数据
        if hasattr(self, 'is_oxygen_kiln'):
            self.is_oxygen_kiln.set("否")
        if hasattr(self, 'has_oxygen_lance'):
            self.has_oxygen_lance.set("否")
        
        # 氧气管道数据
        if hasattr(self, 'oxygen_flow'):
            self.oxygen_flow.set("")
        if hasattr(self, 'oxygen_old_flow'):
            self.oxygen_old_flow.set("")
        if hasattr(self, 'oxygen_inlet_pressure'):
            self.oxygen_inlet_pressure.set("")
        if hasattr(self, 'oxygen_velocity'):
            self.oxygen_velocity.set("")
        
        # 清空小炉数据
        try:
            if hasattr(self, 'oxygen_furnace_data'):
                for furnace in self.oxygen_furnace_data:
                    for key in furnace:
                        if isinstance(furnace[key], tk.StringVar):
                            furnace[key].set("")
        except Exception as e:
            print(f"清空小炉数据时出错: {str(e)}")
        
        # 移除内存中的压缩空气数据
        if hasattr(self, 'saved_air_data'):
            self.saved_air_data = {}
        
        # 清空设备列表
        if hasattr(self, 'equipment_list'):
            self.equipment_list = []
        
        print("表单数据已清空")


    def update_history_display(self):
        """更新历史记录显示"""
        try:
            # 添加调试信息
            print(f"历史文件路径: {self.history_file}")
            print(f"历史文件是否存在: {os.path.exists(self.history_file)}")

            # 清空现有记录
            for item in self.history_tree.get_children():
                self.history_tree.delete(item)

            # 读取历史记录
            history = []  # 初始化为空列表
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                print(f"成功读取历史记录，共{len(history)}条")
            else:
                print("历史文件不存在，将使用空列表")


            # 添加记录到树形视图
            # 确保history已定义且不为空
            # 确保history已定义且不为空
            # 检查history是否存在且不为空
            # 如果history未定义或为空,则初始化为空列表

                # 添加记录到树形视图
            for record in history:
                # 使用get方法获取字段值，提供默认值
                time_value = record.get("时间", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                project_name = record.get("工程名称", "未命名")
                project_code = record.get("工程代号", "")
                project_type = record.get("项目类型", "")
                furnace_count = record.get("小炉数", 0)
                line_count = record.get("一窑几线", 0)

                # 使用获取到的值添加到树形视图
                self.history_tree.insert("", "end", values=(
                    time_value,
                    project_name,
                    project_code,
                    project_type,
                    furnace_count,
                    line_count
                ))

        except Exception as e:
            print(f"更新历史记录显示时详细错误: {str(e)}")
            messagebox.showerror("错误", f"更新历史记录显示时出错: {str(e)}")
    def filter_history(self, *args):
        """过滤历史记录"""
        search_text = self.search_var.get().lower()

        # 清空现有显示
        for item in self.history_tree.get_children():
            self.history_tree.delete(item)

        # 读取历史记录
        if os.path.exists(self.history_file):
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)

            # 过滤并显示记录
            for record in history:
                if (search_text in str(record["工程名称"]).lower() or
                    search_text in str(record.get("工程代号", "")).lower() or
                    search_text in str(record["项目类型"]).lower() or
                    search_text in str(record["小炉数"]).lower() or
                    search_text in str(record["一窑几线"]).lower()):
                    self.history_tree.insert("", "end", values=(
                        record["时间"],
                        record["工程名称"],
                        record.get("工程代号", ""),  # 工程代号
                        record["项目类型"],
                        record["小炉数"],
                        record["一窑几线"]
                    ))

    def create_history_panel(self):
        """创建历史记录面板"""
        # 创建搜索框
        search_frame = ttk.Frame(self.history_frame)
        search_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(search_frame, text="搜索:").pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var)
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        # 【新增】添加删除记录按钮
        delete_button = ttk.Button(search_frame, text="删除记录", command=self.delete_selected_history)
        delete_button.pack(side=tk.RIGHT, padx=5)

        # 创建树形视图
        columns = ("时间", "工程名称", "工程代号", "项目类型", "小炉数", "一窑几线")
        self.history_tree = ttk.Treeview(self.history_frame, columns=columns, show="headings", height=20)  # 设置合适的高度

        # 设置列标题和宽度
        widths = {
            "时间": 75,
            "工程名称": 75,
            "工程代号": 75,  # 【新增】工程代号列宽度
            "项目类型": 75,
            "小炉数": 40,
            "一窑几线": 40
        }

        for col in columns:
            self.history_tree.heading(col, text=col, anchor="center")
            self.history_tree.column(col, width=widths.get(col, 50), anchor="center")

        # 添加垂直滚动条
        y_scrollbar = ttk.Scrollbar(self.history_frame, orient=tk.VERTICAL, command=self.history_tree.yview)
        self.history_tree.configure(yscrollcommand=y_scrollbar.set)

        # 添加水平滚动条
        x_scrollbar = ttk.Scrollbar(self.history_frame, orient=tk.HORIZONTAL, command=self.history_tree.xview)
        self.history_tree.configure(xscrollcommand=x_scrollbar.set)

        # 放置树形视图和滚动条
        self.history_tree.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=5, pady=5)
        y_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        x_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定搜索事件
        self.search_var.trace_add("write", self.filter_history)

        # 绑定双击事件
        self.history_tree.bind("<Double-1>", self.load_selected_history)

    def load_selected_history(self, event=None):
        """加载选中的历史记录项"""
        try:
            # 获取选中项
            selected_item = self.history_tree.selection()[0]
            values = self.history_tree.item(selected_item)['values']

            if values:
                # 读取完整的历史记录数据
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

                # 找到对应的完整记录
                for record in history:
                    # 【修改】使用get方法获取字段值，提供默认值进行比较
                    if record.get("时间", "") == values[0] and record.get("工程名称", "") == values[1]:
                        # 改用load_project方法加载数据，确保数据加载一致性
                        self.load_project(record)
                        return

        except IndexError:
            messagebox.showwarning("警告", "请先选择一条历史记录")
        except Exception as e:
            messagebox.showerror("错误", f"加载历史记录时出错: {str(e)}")
            print(f"详细错误信息: {traceback.format_exc()}")
    def load_normal_gas_data(self, record):
        """加载普通天然气计算数据"""
        try:
                        # 加载0#氧枪和全氧窑设置
                        if "是否有0#氧枪" in record:
                            self.has_oxygen_lance.set(record["是否有0#氧枪"])
                            print(f"从历史记录加载0#氧枪设置: {record['是否有0#氧枪']}")
                        if "是否是全氧窑" in record:
                            self.is_oxygen_kiln.set(record["是否是全氧窑"])
                            print(f"从历史记录加载全氧窑设置: {record['是否是全氧窑']}")
                        # 更新氧气工具按钮状态
                        self.update_oxygen_tools_state()
                        # 填充流量数据
                        flow_data = record["流量数据"]
                        self.old_flow.set(flow_data["窑老期流量"])
                        self.normal_flow.set(flow_data["正常生产时流量"])
                        self.forming_flow.set(flow_data["成型室加热流量"])
                        self.edge_flow.set(flow_data["边火加热流量"])
                        # 填充支通路总流量（如果存在）
                        if "支通路总流量" in flow_data:
                            self.bypass_total_flow.set(flow_data["支通路总流量"])
                        else:
                            self.bypass_total_flow.set("")

                        # 填充压力数据
                        pressure_data = record["压力数据"]
                        self.inlet_pressure.set(pressure_data["进车间压力"])
                        self.main_valve_pre.set(pressure_data["总管调节阀前压力"])
                        self.main_valve_post.set(pressure_data["总管调节阀后压力"])

                        # 填充管径数据
                        diameter_data = record["管径数据"]
                        self.main_pre_diameter.set(diameter_data["总管阀前管径"])
                        self.main_post_diameter.set(diameter_data["总管阀后管径"])
                        self.bypass_main_pre_diameter.set(diameter_data["支通路总管阀前管径"])
                        self.bypass_main_post_diameter.set(diameter_data["支通路总管阀后管径"])
                        # 【新增】加载放散阀数据
                        # 从顶层加载放散阀工作压力、反算d0和选取DN数据
                        if "放散阀工作压力" in record:
                            self.release_valve_working_pressure_value.set(record["放散阀工作压力"])
                            print(f"已加载放散阀工作压力: {record['放散阀工作压力']}")
                        if "放散阀反算d0" in record:
                            self.release_valve_d0_value.set(record["放散阀反算d0"])
                            print(f"已加载放散阀反算d0: {record['放散阀反算d0']}")
                        if "放散阀选取DN" in record:
                            self.release_valve_dn_value.set(record["放散阀选取DN"])
                            print(f"已加载放散阀选取DN: {record['放散阀选取DN']}")
                        if "放散阀截面积A" in record:
                            self.release_valve_area_value.set(record["放散阀截面积A"])
                            print(f"已加载放散阀截面积A: {record['放散阀截面积A']}")
                        if "release_valve_area" in record:
                            self.release_valve_area_value.set(record["release_valve_area"])
                        # 兼容旧字段名
                        if "release_valve_working_pressure" in record:
                            self.release_valve_working_pressure_value.set(record["release_valve_working_pressure"])
                        if "release_valve_d0" in record:
                            self.release_valve_d0_value.set(record["release_valve_d0"])


                         # 兼容新旧字段名
                        if "小炉支管调节阀前压力" in pressure_data:
                            self.branch_valve_pre.set(pressure_data["小炉支管调节阀前压力"])
                        elif "支管调节阀前压力" in pressure_data:
                            self.branch_valve_pre.set(pressure_data["支管调节阀前压力"])

                        if "小炉支管调节阀后压力" in pressure_data:
                            self.branch_valve_post.set(pressure_data["小炉支管调节阀后压力"])
                        elif "支管调节阀后压力" in pressure_data:
                            self.branch_valve_post.set(pressure_data["支管调节阀后压力"])

                        if hasattr(self, 'bypass_vars') and self.bypass_vars:
                            if "边火阀前压力" in pressure_data:
                                self.bypass_vars['branch_pre']['pressure'].set(pressure_data["边火阀前压力"])
                            if "边火阀后压力" in pressure_data:
                                self.bypass_vars['branch_post']['pressure'].set(pressure_data["边火阀后压力"])

                            branch_pre_diameter = diameter_data.get("支通路支管阀前管径", "")
                            branch_post_diameter = diameter_data.get("支通路支管阀后管径", "")

                            # 如果值为空，可以设置一个默认值
                            if not branch_pre_diameter:
                                branch_pre_diameter = ""  # 设置一个合适的默认值
                            if not branch_post_diameter:
                                branch_post_diameter = ""  # 设置一个合适的默认值

                            # 设置到界面上
                            self.bypass_vars['branch_pre']['selected'].set(branch_pre_diameter)
                            self.bypass_vars['branch_post']['selected'].set(branch_post_diameter)
                              # 【新增】保存流速记录，优先使用历史记录中的流速值
                            velocity_data = record.get("流速数据", {})
                            if "反算支管阀前流速" in velocity_data:
                                try:
                                    velocity_value = float(velocity_data["反算支管阀前流速"])
                                    velocity_text = velocity_data["反算支管阀前流速"]

                                    # 设置颜色基于流速值
                                    if velocity_value <= 20:
                                        self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                                    elif velocity_value <= 25:
                                        self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                                    else:
                                        self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
                                except (ValueError, TypeError):
                                    # 如果无法解析为数字，则不设置颜色
                                    self.bypass_vars['branch_pre']['velocity'].configure(text=velocity_data["反算支管阀前流速"])

                            if "反算支管阀后流速" in velocity_data:
                                try:
                                    velocity_value = float(velocity_data["反算支管阀后流速"])
                                    velocity_text = velocity_data["反算支管阀后流速"]

                                    # 设置颜色基于流速值
                                    if velocity_value <= 20:
                                        self.bypass_vars['branch_post']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                                    elif velocity_value <= 25:
                                        self.bypass_vars['branch_post']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                                    else:
                                        self.bypass_vars['branch_post']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
                                except (ValueError, TypeError):
                                    # 如果无法解析为数字，则不设置颜色
                                    self.bypass_vars['branch_post']['velocity'].configure(text=velocity_data["反算支管阀后流速"])
                            # 支通路总管阀前/后流速颜色设置
                            if "反算总管阀前流速" in velocity_data:
                                try:
                                    velocity_value = float(velocity_data["反算总管阀前流速"])
                                    velocity_text = velocity_data["反算总管阀前流速"]

                                    # 设置颜色基于流速值
                                    if velocity_value <= 20:
                                        self.bypass_vars['main_pre']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                                    elif velocity_value <= 25:
                                        self.bypass_vars['main_pre']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                                    else:
                                        self.bypass_vars['main_pre']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
                                except (ValueError, TypeError):
                                    # 如果无法解析为数字，则不设置颜色
                                    self.bypass_vars['main_pre']['velocity'].configure(text=velocity_data["反算总管阀前流速"])

                            if "反算总管阀后流速" in velocity_data:
                                try:
                                    velocity_value = float(velocity_data["反算总管阀后流速"])
                                    velocity_text = velocity_data["反算总管阀后流速"]

                                    # 设置颜色基于流速值
                                    if velocity_value <= 20:
                                        self.bypass_vars['main_post']['velocity'].configure(text=velocity_text, foreground="#009900")  # 绿色 - 正常范围
                                    elif velocity_value <= 25:
                                        self.bypass_vars['main_post']['velocity'].configure(text=velocity_text, foreground="#ff9900")  # 橙色 - 注意范围
                                    else:
                                        self.bypass_vars['main_post']['velocity'].configure(text=velocity_text, foreground="#cc0000")  # 红色 - 危险范围
                                except (ValueError, TypeError):
                                    # 如果无法解析为数字，则不设置颜色
                                    self.bypass_vars['main_post']['velocity'].configure(text=velocity_data["反算总管阀后流速"])


                             # 【调试】添加打印语句查看加载的值
                            print(f"加载支通路支管阀前管径: {branch_pre_diameter}")
                            print(f"加载支通路支管阀后管径: {branch_post_diameter}")

                        # 【新增】加载支通路压力数据（如果存在）
                        if "支通路总管阀前压力" in pressure_data:
                            self.bypass_main_valve_pre.set(pressure_data["支通路总管阀前压力"])
                        else:
                            self.bypass_main_valve_pre.set("")  # 如果不存在则设置为空

                        if "支通路总管阀后压力" in pressure_data:
                            self.bypass_main_valve_post.set(pressure_data["支通路总管阀后压力"])
                        else:
                            self.bypass_main_valve_post.set("")  # 如果不存在则设置为空

                        # 填充流速温度数据
                        velocity_temp_data = record["流速温度数据"]
                        self.main_velocity.set(velocity_temp_data["天然气总管流速"])
                        self.branch_velocity.set(velocity_temp_data["小炉支管流速"])
                        self.temperature.set(velocity_temp_data["设计温度"])

                        if "main_valve_c_selected" in record:
                            self.main_valve_c_selected_value.set(record["main_valve_c_selected"])
                        if "main_valve_k_max" in record:
                            self.main_valve_k_max_value.set(record["main_valve_k_max"])
                        if "main_valve_k_min" in record:
                            self.main_valve_k_min_value.set(record["main_valve_k_min"])
                        if "release_valve_dn" in record:
                            self.release_valve_dn_value.set(record["release_valve_dn"])
                        # 【修改】判断是否已经从历史记录中加载了流速值
                        velocity_data = record.get("流速数据", {})
                        if not ("反算支管阀前流速" in velocity_data and "反算支管阀后流速" in velocity_data):
                            # 如果历史记录中没有流速数据，才重新计算流速
                            if hasattr(self, 'bypass_vars') and self.bypass_vars:
                                # 调用自动计算方法来更新流速
                                self.auto_calculate_branch_pre_velocity()
                                self.auto_calculate_branch_post_velocity()

                        # 加载小炉阀门数据
                        if "small_valve_data" in record:
                            self.small_valve_persistent_data = record["small_valve_data"]

                        # 更新小炉显示
                        self.update_furnace_display()
                        # 确保所有流速颜色正确显示
                        # 确保所有流速颜色正确显示
                        self.refresh_all_velocities_colors()
                        print(f"支通路总管阀前流速颜色: {self.bypass_vars['main_pre']['velocity'].cget('foreground')}")
                        print(f"总管阀前流速颜色: {self.main_pre_velocity.cget('foreground')}")
                        print(f"支通路总管阀前流速颜色: {self.bypass_vars['main_pre']['velocity'].cget('foreground')}")

                        # 填充小炉数据
                        for i, furnace_data in enumerate(record["小炉数据"]):
                            if i < len(self.furnace_data):
                                self.furnace_data[i]['heat_load'].set(furnace_data["平均热负荷"])
                                self.furnace_data[i]['float_value'].set(furnace_data["浮动值"])
                                self.furnace_data[i]['nozzle_count'].set(furnace_data["喷枪数"])
                    # 兼容旧版和新版字段名
                                if "选取阀前管径" in furnace_data:
                                    self.furnace_data[i]['selected_diameter'].set(furnace_data["选取阀前管径"])
                                elif "选取管径" in furnace_data:
                                    self.furnace_data[i]['selected_diameter'].set(furnace_data["选取管径"])

                    # 设置阀后管径（如果存在）
                                if "选取阀后管径" in furnace_data and 'selected_post_diameter' in self.furnace_data[i]:
                                    self.furnace_data[i]['selected_post_diameter'].set(furnace_data["选取阀后管径"])

            # 自动触发所有计算
                        self.auto_calculate_main_pipe()
                        self.auto_calculate_bypass_pipe()
                        # 触发计算小炉值
                        self.calculate_furnace_values()
        except Exception as e:
            print(f"加载天然气数据出错: {str(e)}")
            raise
    def refresh_all_velocities_colors(self):
        """强制刷新所有流速的颜色显示，包括总管和支通路"""
        try:
            # 获取当前主题的颜色设置
            normal_color = getattr(self, 'velocity_colors', {}).get('normal', "#009900")  # 默认绿色
            warning_color = getattr(self, 'velocity_colors', {}).get('warning', "#ff9900")  # 默认橙色
            danger_color = getattr(self, 'velocity_colors', {}).get('danger', "#cc0000")  # 默认红色

            # 刷新支通路流速颜色
            self.refresh_bypass_velocities_colors()

            # 刷新总管阀前流速颜色
            try:
                main_pre_velocity_text = self.main_pre_velocity.cget("text")
                if main_pre_velocity_text:
                    velocity_value = float(main_pre_velocity_text)
                    if velocity_value <= 20:
                        self.main_pre_velocity.configure(foreground="#009900")  # 绿色 - 正常范围
                    elif velocity_value <= 25:
                        self.main_pre_velocity.configure(foreground="#ff9900")  # 橙色 - 注意范围
                    else:
                        self.main_pre_velocity.configure(foreground="#cc0000")  # 红色 - 危险范围
                    print(f"总管阀前流速: {main_pre_velocity_text}, 设置颜色: {self.main_pre_velocity.cget('foreground')}")
            except (ValueError, TypeError) as e:
                print(f"刷新总管阀前流速颜色时出错: {str(e)}")

            # 刷新总管阀后流速颜色
            try:
                main_post_velocity_text = self.main_post_velocity.cget("text")
                if main_post_velocity_text:
                    velocity_value = float(main_post_velocity_text)
                    if velocity_value <= 20:
                        self.main_post_velocity.configure(foreground="#009900")  # 绿色 - 正常范围
                    elif velocity_value <= 25:
                        self.main_post_velocity.configure(foreground="#ff9900")  # 橙色 - 注意范围
                    else:
                        self.main_post_velocity.configure(foreground="#cc0000")  # 红色 - 危险范围
            except (ValueError, TypeError):
                pass
            # 刷新小炉区流速颜色
            try:
                for furnace in self.furnace_data:
                    # 刷新阀前流速颜色
                    try:
                        calc_velocity_text = furnace['calc_velocity'].cget("text")
                        if calc_velocity_text:
                            velocity_value = float(calc_velocity_text)
                            if velocity_value <= 20:
                                furnace['calc_velocity'].configure(foreground="#009900")  # 绿色 - 正常范围
                            elif velocity_value <= 25:
                                furnace['calc_velocity'].configure(foreground="#ff9900")  # 橙色 - 注意范围
                            else:
                                furnace['calc_velocity'].configure(foreground="#cc0000")  # 红色 - 危险范围
                    except (ValueError, TypeError, KeyError):
                        pass

                    # 刷新阀后流速颜色
                    try:
                        if 'calc_post_velocity' in furnace:
                            calc_post_velocity_text = furnace['calc_post_velocity'].cget("text")
                            if calc_post_velocity_text:
                                velocity_value = float(calc_post_velocity_text)
                                if velocity_value <= 20:
                                    furnace['calc_post_velocity'].configure(foreground="#009900")  # 绿色 - 正常范围
                                elif velocity_value <= 25:
                                    furnace['calc_post_velocity'].configure(foreground="#ff9900")  # 橙色 - 注意范围
                                else:
                                    furnace['calc_post_velocity'].configure(foreground="#cc0000")  # 红色 - 危险范围
                    except (ValueError, TypeError, KeyError):
                        pass
            except Exception as e:
                print(f"刷新小炉区流速颜色时出错: {str(e)}")
        except Exception as e:
            print(f"刷新所有流速颜色时出错: {str(e)}")
    def refresh_bypass_velocities_colors(self):
        """强制刷新所有支通路流速的颜色显示"""
        try:
            # 获取当前主题的颜色设置
            normal_color = getattr(self, 'velocity_colors', {}).get('normal', "#009900")  # 默认绿色
            warning_color = getattr(self, 'velocity_colors', {}).get('warning', "#ff9900")  # 默认橙色
            danger_color = getattr(self, 'velocity_colors', {}).get('danger', "#cc0000")  # 默认红色

            # 刷新支通路总管阀前流速颜色
            try:
                main_pre_velocity_text = self.bypass_vars['main_pre']['velocity'].cget("text")
                if main_pre_velocity_text:
                    velocity_value = float(main_pre_velocity_text)
                    if velocity_value <= 20:
                        self.bypass_vars['main_pre']['velocity'].configure(foreground="#009900")  # 绿色 - 正常范围
                    elif velocity_value <= 25:
                        self.bypass_vars['main_pre']['velocity'].configure(foreground="#ff9900")  # 橙色 - 注意范围
                    else:
                        self.bypass_vars['main_pre']['velocity'].configure(foreground="#cc0000")  # 红色 - 危险范围
            except (ValueError, TypeError):
                pass

            # 刷新支通路总管阀后流速颜色
            try:
                main_post_velocity_text = self.bypass_vars['main_post']['velocity'].cget("text")
                if main_post_velocity_text:
                    velocity_value = float(main_post_velocity_text)
                    if velocity_value <= 20:
                        self.bypass_vars['main_post']['velocity'].configure(foreground="#009900")  # 绿色 - 正常范围
                    elif velocity_value <= 25:
                        self.bypass_vars['main_post']['velocity'].configure(foreground="#ff9900")  # 橙色 - 注意范围
                    else:
                        self.bypass_vars['main_post']['velocity'].configure(foreground="#cc0000")  # 红色 - 危险范围
            except (ValueError, TypeError):
                pass

            # 刷新边火支管阀前流速颜色
            try:
                branch_pre_velocity_text = self.bypass_vars['branch_pre']['velocity'].cget("text")
                if branch_pre_velocity_text:
                    velocity_value = float(branch_pre_velocity_text)
                    if velocity_value <= 20:
                        self.bypass_vars['branch_pre']['velocity'].configure(foreground="#009900")  # 绿色 - 正常范围
                    elif velocity_value <= 25:
                        self.bypass_vars['branch_pre']['velocity'].configure(foreground="#ff9900")  # 橙色 - 注意范围
                    else:
                        self.bypass_vars['branch_pre']['velocity'].configure(foreground="#cc0000")  # 红色 - 危险范围
            except (ValueError, TypeError):
                pass

            # 刷新边火支管阀后流速颜色
            try:
                branch_post_velocity_text = self.bypass_vars['branch_post']['velocity'].cget("text")
                if branch_post_velocity_text:
                    velocity_value = float(branch_post_velocity_text)
                    if velocity_value <= 20:
                        self.bypass_vars['branch_post']['velocity'].configure(foreground="#009900")  # 绿色 - 正常范围
                    elif velocity_value <= 25:
                        self.bypass_vars['branch_post']['velocity'].configure(foreground="#ff9900")  # 橙色 - 注意范围
                    else:
                        self.bypass_vars['branch_post']['velocity'].configure(foreground="#cc0000")  # 红色 - 危险范围
            except (ValueError, TypeError):
                pass
        except Exception as e:
            print(f"刷新流速颜色时出错: {str(e)}")
    def delete_selected_history(self):
        """删除选中的历史记录"""
        try:
            # 获取选中的项
            selected_items = self.history_tree.selection()
            if not selected_items:
                messagebox.showinfo("提示", "请先选择要删除的记录")
                return

            # 确认是否删除
            confirm = messagebox.askyesno("确认", "确定要删除选中的记录吗？此操作不可撤销。")
            if not confirm:
                return

            # 获取选中项的时间和工程名称
            selected_item = selected_items[0]
            values = self.history_tree.item(selected_item)['values']
            selected_time = values[0]
            selected_project = values[1]

            # 打印调试信息
            print(f"尝试删除记录: 时间={selected_time}, 工程名称={selected_project}, 类型={type(selected_project)}")

            # 读取历史记录
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

                # 查找并删除记录 - 使用字符串比较
                new_history = []
                deleted = False
                for record in history:
                    # 同时比较时间和工程名称，并确保都转换为字符串进行比较
                    if str(record["时间"]) != str(selected_time) or str(record["工程名称"]) != str(selected_project):
                        new_history.append(record)
                    else:
                        deleted = True
                        print(f"删除记录: {record['工程名称']}")

                # 保存更新后的历史记录
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump(new_history, f, ensure_ascii=False, indent=2)

                # 从树形视图中删除选中项
                self.history_tree.delete(selected_item)

                # 显示删除结果
                if deleted:
                    messagebox.showinfo("成功", "记录已成功删除")
                else:
                    messagebox.showwarning("警告", "未找到要删除的记录，可能是数据类型不匹配")

        except Exception as e:
            messagebox.showerror("错误", f"删除记录时出错: {str(e)}")
            print(f"删除记录时出错: {str(e)}")
            # 打印详细错误信息
            import traceback
            print(traceback.format_exc())

    def load_history(self):
        """加载历史记录"""

        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)

                # 过滤掉空白记录 - 添加此逻辑
                filtered_history = []
                for record in history:
                    # 只保留有工程名称或工程代号的记录
                    if record.get("工程名称") or record.get("工程代号"):
                        filtered_history.append(record)

                # 如果有记录被过滤掉，保存过滤后的历史记录
                if len(filtered_history) < len(history):
                    print(f"已过滤{len(history) - len(filtered_history)}条空白记录")
                    with open(self.history_file, 'w', encoding='utf-8') as f:
                        json.dump(filtered_history, f, ensure_ascii=False, indent=2)
                    history = filtered_history

                # 检查并修复历史记录中可能缺少的字段
                for record in history:
                    # 【修改】确保基本字段存在
                    if "时间" not in record:
                        record["时间"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    if "工程名称" not in record:
                        record["工程名称"] = "未命名"
                    if "工程代号" not in record:
                        record["工程代号"] = ""
                    if "项目类型" not in record:
                        record["项目类型"] = ""
                    if "吨位" not in record:  # 新增吨位字段检查
                        record["吨位"] = ""
                    if "小炉数" not in record:
                        record["小炉数"] = 1
                    if "一窑几线" not in record:
                        record["一窑几线"] = 1

                    # 确保流量数据、压力数据、管径数据和流速温度数据字段存在
                    if "流量数据" not in record:
                        record["流量数据"] = {}
                    if "压力数据" not in record:
                        record["压力数据"] = {}
                    if "管径数据" not in record:
                        record["管径数据"] = {}
                    if "流速温度数据" not in record:
                        record["流速温度数据"] = {}
                    if "小炉数据" in record:
                        for furnace in record["小炉数据"]:
                            # 确保新增字段存在
                            if "选取阀后管径" not in furnace:
                                furnace["选取阀后管径"] = ""
                            if "计算阀后管径" not in furnace:
                                furnace["计算阀后管径"] = ""
                            if "反算阀后流速" not in furnace:
                                furnace["反算阀后流速"] = ""
                    # 【新增】检查压力数据字段
                    if "压力数据" in record:
                        if "支通路总管阀前压力" not in record["压力数据"]:
                            record["压力数据"]["支通路总管阀前压力"] = ""
                        if "支通路总管阀后压力" not in record["压力数据"]:
                            record["压力数据"]["支通路总管阀后压力"] = ""
                    # 【新增】检查管径数据字段
                    if "管径数据" in record:
                        if "支通路总管阀前管径" not in record["管径数据"]:
                            record["管径数据"]["支通路总管阀前管径"] = ""
                        if "支通路总管阀后管径" not in record["管径数据"]:
                            record["管径数据"]["支通路总管阀后管径"] = ""
                        if "支通路支管阀前管径" not in record["管径数据"]:
                            record["管径数据"]["支通路支管阀前管径"] = ""
                        if "支通路支管阀后管径" not in record["管径数据"]:
                            record["管径数据"]["支通路支管阀后管径"] = ""
                # 保存修复后的历史记录
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump(history, f, ensure_ascii=False, indent=2)

                # 直接在主界面的历史记录区域显示数据
                for record in history:
                    # 【修改】使用get方法获取字段值，提供默认值
                    self.history_tree.insert("", "end", values=(
                        record.get("时间", ""),
                        record.get("工程名称", ""),
                        record.get("工程代号", ""),
                        record.get("项目类型", ""),
                        record.get("小炉数", ""),
                        record.get("一窑几线", "")
                ))
            else:
                # 如果历史记录文件不存在，创建一个空文件
                history = []
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump([], f)

        except Exception as e:
            messagebox.showerror("错误", f"加载历史记录时出错: {str(e)}")
    def sync_main_pre_diameter(self, *args):
        """同步总管阀前管径选取到输出区"""
        try:
            # 将选取管径栏的值同步到输出区
            self.main_pre_selected.set(self.main_pre_diameter.get())
        except Exception as e:
            pass

    def sync_main_post_diameter(self, *args):
        """同步总管阀后管径选取到输出区"""
        try:
            # 将选取管径栏的值同步到输出区
            self.main_post_selected.set(self.main_post_diameter.get())
        except Exception as e:
            pass

    def sync_main_pre_selected(self, *args):
        """同步输出区总管阀前管径选取回选取管径栏"""
        try:
            # 将输出区的值同步回选取管径栏
            self.main_pre_diameter.set(self.main_pre_selected.get())
        except Exception as e:
            pass

    def sync_main_post_selected(self, *args):
        """同步输出区总管阀后管径选取回选取管径栏"""
        try:
            # 将输出区的值同步回选取管径栏
            self.main_post_diameter.set(self.main_post_selected.get())
        except Exception as e:
            pass
    def sync_bypass_main_pre_diameter(self, *args):
        """同步支通路总管阀前管径选取到输出区"""
        try:
            # 将选取管径栏的值同步到输出区
            self.bypass_vars['main_pre']['selected'].set(self.bypass_main_pre_diameter.get())
        except Exception as e:
            pass

    def sync_bypass_main_post_diameter(self, *args):
        """同步支通路总管阀后管径选取到输出区"""
        try:
            # 将选取管径栏的值同步到输出区
            self.bypass_vars['main_post']['selected'].set(self.bypass_main_post_diameter.get())
        except Exception as e:
            pass

    def sync_bypass_main_pre_selected(self, *args):
        """同步输出区支通路总管阀前管径选取回选取管径栏"""
        try:
            # 将输出区的值同步回选取管径栏
            self.bypass_main_pre_diameter.set(self.bypass_vars['main_pre']['selected'].get())
        except Exception as e:
            pass

    def sync_bypass_main_post_selected(self, *args):
        """同步输出区支通路总管阀后管径选取回选取管径栏"""
        try:
            # 将输出区的值同步回选取管径栏
            self.bypass_main_post_diameter.set(self.bypass_vars['main_post']['selected'].get())
        except Exception as e:
            pass
    # 在GasCalculator类中添加以下方法

    def show_self_operated_valve_calculator(self):
        """显示自力式阀计算窗口"""
        # 使用封装的自力式阀计算类
        self.self_operated_valve.show_calculator(self.root)


    def load_project(self, project_data):
        """加载项目数据"""
        try:
            # 设置current_data，确保压缩空气计算器能够正确加载数据
            self.current_data = project_data.copy()
            # 加载基本项目信息
            self.project_name.set(project_data.get("工程名称", ""))
            self.project_code.set(project_data.get("工程代号", ""))
            self.project_type.set(project_data.get("项目类型", ""))
            self.daily_capacity.set(project_data.get("吨位", ""))  # 新增吨位加载
            self.furnace_count.set(project_data.get("小炉数", 1))
            self.line_count.set(project_data.get("一窑几线", 1))
             # 在这里之后，添加从顶层加载小炉阀门数据的代码
            # 加载主管调节阀数据
            self.main_valve_c_selected_value.set(project_data.get("主管调节阀C选定", ""))
            self.main_valve_k_max_value.set(project_data.get("主管调节阀K大值", ""))
            self.main_valve_k_min_value.set(project_data.get("主管调节阀K小值", ""))
            self.main_valve_core_size_value.set(project_data.get("主管调节阀阀芯尺寸", ""))
            # 添加这一行来加载主管调节阀管径
            self.main_valve_diameter_value.set(project_data.get("主管调节阀管径", ""))
            # 从顶层加载小炉阀门数据
            furnace_count = self.furnace_count.get()

            # 确保small_valve_persistent_data已初始化
            if not hasattr(self, 'small_valve_persistent_data'):
                self.small_valve_persistent_data = []

            # 调整small_valve_persistent_data大小与furnace_count一致
            while len(self.small_valve_persistent_data) < furnace_count:
                self.small_valve_persistent_data.append({})

            # 从顶层读取小炉阀门数据
            for i in range(furnace_count):
                c_selected = project_data.get(f"小炉{i+1}调节阀C选定", "")
                k_max = project_data.get(f"小炉{i+1}调节阀K大值", "")
                k_min = project_data.get(f"小炉{i+1}调节阀K小值", "")

                # 确保索引有效
                if i < len(self.small_valve_persistent_data):
                    self.small_valve_persistent_data[i]['c_selected'] = c_selected
                    self.small_valve_persistent_data[i]['k_max'] = k_max
                    self.small_valve_persistent_data[i]['k_min'] = k_min
                # 【新增】从顶层加载用气需求数据
            self.air_pressure.set(project_data.get("进车间压力(MPa)", ""))
            self.air_velocity.set(project_data.get("设计流速(m/s)", ""))
            self.air_continuous_total.set(project_data.get("连续用气总量(Nm³/h)", ""))
            self.air_intermittent_total.set(project_data.get("间歇用气总量(Nm³/h)", ""))
            self.air_total_flow.set(project_data.get("总用气量(Nm³/h)", ""))
            self.air_calculated_diameter.set(project_data.get("计算管径(mm)", ""))
            self.air_standard_diameter.set(project_data.get("选取管径(mm)", ""))
            self.air_actual_velocity.set(project_data.get("实际流速(m/s)", ""))
            
            # 【新增】加载用气明细数据
            if hasattr(self, 'air_input_vars'):
                self.air_input_vars["regenerator_purge"].set(project_data.get("蓄热室吹扫用气(间歇Nm³/h)", ""))
                self.air_input_vars["gun_cooling"].set(project_data.get("喷枪冷却用气(连续Nm³/h)", ""))
                self.air_input_vars["feeder"].set(project_data.get("投料机用气(连续Nm³/h)", ""))
                self.air_input_vars["valve_tv"].set(project_data.get("阀及工业电视用气(连续Nm³/h)", ""))
                self.air_input_vars["cold_end"].set(project_data.get("冷端机组用气(连续Nm³/h)", ""))
                self.air_input_vars["annealing_ir"].set(project_data.get("退火窑及红外用气(连续Nm³/h)", ""))
                self.air_input_vars["branch_heating"].set(project_data.get("支通路加热用气(间歇Nm³/h)", ""))
                self.air_input_vars["rolling_burn"].set(project_data.get("压延机烧边火用气(连续Nm³/h)", ""))
                self.air_input_vars["rolling_clean"].set(project_data.get("压延机清理用气(间歇Nm³/h)", ""))

            # 尝试兼容旧格式数据（不包含单位的键）
            if not self.air_pressure.get() and "进车间压力" in project_data:
                self.air_pressure.set(project_data.get("进车间压力", ""))
            if not self.air_velocity.get() and "设计流速" in project_data:
                self.air_velocity.set(project_data.get("设计流速", ""))
            # 更新氧枪相关字段 - 统一处理一次
            if "是否有0#氧枪" in project_data:
                has_lance = project_data.get("是否有0#氧枪")
                # 标准化处理，支持多种格式
                if isinstance(has_lance, bool):
                    self.has_oxygen_lance.set("是" if has_lance else "否")
                elif has_lance in ["1", "true", "True", "是"]:
                    self.has_oxygen_lance.set("是")
                else:
                    self.has_oxygen_lance.set("否")

                print(f"设置0#氧枪开关: {self.has_oxygen_lance.get()}")

            # 更新全氧窑相关字段
            if "是否是全氧窑" in project_data:
                is_kiln = project_data.get("是否是全氧窑")
                if isinstance(is_kiln, bool):
                    self.is_oxygen_kiln.set("是" if is_kiln else "否")
                elif is_kiln in ["1", "true", "True", "是"]:
                    self.is_oxygen_kiln.set("是")
                else:
                    self.is_oxygen_kiln.set("否")
            # 恢复小炉阀门持久化数据
            if "small_valve_data" in project_data:
                self.small_valve_persistent_data = project_data["small_valve_data"]
                print(f"已加载小炉阀门数据: {self.small_valve_persistent_data}")

            # 如果存在氧枪数据且氧枪开关是"是"，则主动加载氧枪数据
            if self.has_oxygen_lance.get() == "是":
                try:
                    # 确保传递项目数据给氧枪计算器
                    self.oxygen_lance_calculator.load_data()
                    print("已加载0#氧枪计算器数据")
                except Exception as e:
                    print(f"加载氧枪计算器数据时出错: {str(e)}")
                    traceback.print_exc()
             # 修改这一部分，将record_data改为project_data
            if any(key.startswith("压缩空气") or key in ["进车间压力", "设计流速", "连续用气总量"] for key in project_data):
                print("发现压缩空气相关数据，准备加载...")
                # 确保compressed_air_calculator已初始化
                if not hasattr(self, 'compressed_air_calculator') or self.compressed_air_calculator is None:
                    from compressed_air import CompressedAirCalculator
                    self.compressed_air_calculator = CompressedAirCalculator(self)
                    print("重新初始化压缩空气计算器")

                # 获取当前项目标识
                project_name = self.project_name.get()
                project_code = self.project_code.get()
                current_project_id = f"{project_name}_{project_code}"

                # 如果压缩空气窗口已打开，则直接更新窗口内容为当前项目的数据，而不是销毁重建
                if hasattr(self, 'compressed_air_calculator') and hasattr(self.compressed_air_calculator, 'air_window') and \
                self.compressed_air_calculator.air_window and self.compressed_air_calculator.air_window.winfo_exists():
                    # 直接更新数据，不销毁窗口
                    try:
                        # 先保存窗口位置信息，以防万一
                        window_geometry = self.compressed_air_calculator.air_window.geometry()

                        # 直接调用加载数据方法，更新窗口内容
                        self.compressed_air_calculator.load_data_from_record(project_data)

                        print(f"已更新压缩空气窗口内容为项目 {current_project_id} 的数据")
                    except Exception as e:
                        print(f"更新压缩空气窗口内容时出错: {str(e)}")
                        traceback.print_exc()

                # 创建项目相关的压缩空气数据
                air_data = {}
                for key, value in project_data.items():
                    if (key.startswith("压缩空气") or
                        key.startswith("蓄热室吹扫用气") or
                        key.startswith("喷枪冷却用气") or
                        key.startswith("投料机用气") or
                        key.startswith("阀及工业电视用气") or
                        key.startswith("冷端机组用气") or
                        key.startswith("退火窑及红外用气") or
                        key.startswith("支通路加热用气") or
                        key.startswith("压延机烧边火用气") or
                        key.startswith("压延机清理用气") or
                        key == "进车间压力" or
                        key == "设计流速" or
                        key == "连续用气总量" or
                        key == "间歇用气总量" or
                        key == "总用气量" or
                        key == "计算管径" or
                        key == "选取管径" or
                        key == "实际流速" or
                        key == "进车间压力(MPa)" or
                        key == "设计流速(m/s)" or
                        key == "连续用气总量(Nm³/h)" or
                        key == "间歇用气总量(Nm³/h)" or
                        key == "总用气量(Nm³/h)" or
                        key == "计算管径(mm)" or
                        key == "选取管径(mm)" or
                        key == "实际流速(m/s)"):
                        air_data[key] = value

                # 保存该项目的压缩空气数据到内存中
                if hasattr(self, 'saved_air_data'):
                    if not isinstance(self.saved_air_data, dict):
                        self.saved_air_data = {}
                else:
                    self.saved_air_data = {}

                # 存储当前项目的压缩空气数据
                if air_data:
                    self.saved_air_data[current_project_id] = air_data
                    print(f"已将{current_project_id}项目的压缩空气数据存储到内存")

                # 尝试加载数据
                try:
                    self.compressed_air_calculator.load_data_from_record(project_data)
                    print("成功调用压缩空气计算器的load_data_from_record方法")

                    # 手动处理支管数据 - 确保即使计算器未打开也能保存数据
                    if "压缩空气支管数据" in project_data:
                        branch_data = project_data["压缩空气支管数据"]
                        print(f"准备处理 {len(branch_data)} 个压缩空气支管数据")

                        # 创建支管变量
                        if not hasattr(self.compressed_air_calculator, 'branch_data'):
                            self.compressed_air_calculator.branch_data = {}

                        for branch in branch_data:
                            idx = branch.get("支管编号", 0)
                            if idx > 0:
                                self.compressed_air_calculator.branch_data[idx] = {
                                    "description": tk.StringVar(value=branch.get("支管描述", "")),
                                    "flow": tk.StringVar(value=branch.get("流量", "")),
                                    "pressure": tk.StringVar(value=branch.get("压力", "")),
                                    "calculated_diameter": tk.StringVar(value=branch.get("计算管径", "")),
                                    "selected_diameter": tk.StringVar(value=branch.get("选取管径", "")),
                                    "actual_velocity": tk.StringVar(value=branch.get("实际流速", ""))
                                }
                                print(f"已保存支管 {idx} 数据到内存")

                    # 同时处理单独的支管数据字段
                    for i in range(1, 10):  # 处理最多9个支管
                        desc_key = f"压缩空气支管{i}描述"
                        if desc_key in project_data and project_data[desc_key]:
                            if not hasattr(self.compressed_air_calculator, 'branch_data'):
                                self.compressed_air_calculator.branch_data = {}

                            if i not in self.compressed_air_calculator.branch_data:
                                self.compressed_air_calculator.branch_data[i] = {
                                    "description": tk.StringVar(value=project_data.get(f"压缩空气支管{i}描述", "")),
                                    "flow": tk.StringVar(value=project_data.get(f"压缩空气支管{i}用气量(Nm³/h)", "")),
                                    "pressure": tk.StringVar(value=project_data.get(f"压缩空气支管{i}压力(MPa)", "")),
                                    "calculated_diameter": tk.StringVar(value=project_data.get(f"压缩空气支管{i}计算管径(mm)", "")),
                                    "selected_diameter": tk.StringVar(value=project_data.get(f"压缩空气支管{i}选取管径(mm)", "")),
                                    "actual_velocity": tk.StringVar(value=project_data.get(f"压缩空气支管{i}实际流速(m/s)", ""))
                                }
                                print(f"已从独立字段保存支管 {i} 数据到内存")
                except Exception as e:
                    print(f"加载压缩空气数据出错: {str(e)}")
                    traceback.print_exc()
            # 更新氧气工具按钮状态
            self.update_oxygen_tools_state()

            # 加载流量数据
            flow_data = project_data.get("流量数据", {})
            self.old_flow.set(flow_data.get("窑老期流量", ""))
            self.normal_flow.set(flow_data.get("正常生产时流量", ""))
            self.forming_flow.set(flow_data.get("成型室加热流量", ""))
            self.edge_flow.set(flow_data.get("边火加热流量", ""))
            self.bypass_total_flow.set(flow_data.get("支通路总流量", ""))

            # 加载压力数据
            pressure_data = project_data.get("压力数据", {})
            self.inlet_pressure.set(pressure_data.get("进车间压力", ""))
            self.main_valve_pre.set(pressure_data.get("总管调节阀前压力", ""))
            self.main_valve_post.set(pressure_data.get("总管调节阀后压力", ""))
            self.branch_valve_pre.set(pressure_data.get("小炉支管调节阀前压力", ""))
            self.branch_valve_post.set(pressure_data.get("小炉支管调节阀后压力", ""))
            self.bypass_main_valve_pre.set(pressure_data.get("支通路总管阀前压力", ""))
            self.bypass_main_valve_post.set(pressure_data.get("支通路总管阀后压力", ""))


            # 【新增】加载边火阀前后压力
            if hasattr(self, 'bypass_vars') and self.bypass_vars:
                self.bypass_vars['branch_pre']['pressure'].set(pressure_data.get("边火阀前压力", ""))
                self.bypass_vars['branch_post']['pressure'].set(pressure_data.get("边火阀后压力", ""))


            # 加载管径数据
            diameter_data = project_data.get("管径数据", {})
            self.main_pre_diameter.set(diameter_data.get("总管阀前管径", ""))
            self.main_post_diameter.set(diameter_data.get("总管阀后管径", ""))
            self.bypass_main_pre_diameter.set(diameter_data.get("支通路总管阀前管径", ""))
            self.bypass_main_post_diameter.set(diameter_data.get("支通路总管阀后管径", ""))
            print(f"加载的支通路支管阀前管径: {diameter_data.get('支通路支管阀前管径', '')}")
            print(f"加载的支通路支管阀后管径: {diameter_data.get('支通路支管阀后管径', '')}")

             # 【修改】加载边火支管管径 - 使用正确的变量路径
            if hasattr(self, 'bypass_vars') and self.bypass_vars:
                # 加载选取管径
                self.bypass_vars['branch_pre']['selected'].set(diameter_data.get("支通路支管阀前管径", ""))
                self.bypass_vars['branch_post']['selected'].set(diameter_data.get("支通路支管阀后管径", ""))

                # 【新增】加载边火阀前后压力
                pressure_data = project_data.get("压力数据", {})
                self.bypass_vars['branch_pre']['pressure'].set(pressure_data.get("边火阀前压力", ""))
                self.bypass_vars['branch_post']['pressure'].set(pressure_data.get("边火阀后压力", ""))
             # 加载主管调节阀数据
            if "main_valve_c_selected" in project_data:
                self.main_valve_c_selected_value.set(project_data["main_valve_c_selected"])
                print(f"已加载主管调节阀C选定值: {project_data['main_valve_c_selected']}")

            if "main_valve_k_max" in project_data:
                self.main_valve_k_max_value.set(project_data["main_valve_k_max"])
                print(f"已加载主管调节阀K大值: {project_data['main_valve_k_max']}")

            if "main_valve_k_min" in project_data:
                self.main_valve_k_min_value.set(project_data["main_valve_k_min"])
                print(f"已加载主管调节阀K小值: {project_data['main_valve_k_min']}")

            # 加载放散阀数据
            if "release_valve_dn" in project_data:
                self.release_valve_dn_value.set(project_data["release_valve_dn"])
                print(f"已加载放散阀选取DN: {project_data['release_valve_dn']}")

            if "release_valve_working_pressure" in project_data:
                self.release_valve_working_pressure_value.set(project_data["release_valve_working_pressure"])
                print(f"已加载放散阀工作压力: {project_data['release_valve_working_pressure']}")

            if "release_valve_d0" in project_data:
                self.release_valve_d0_value.set(project_data["release_valve_d0"])
                print(f"已加载放散阀反算d0: {project_data['release_valve_d0']}")

            if "release_valve_area" in project_data:
                self.release_valve_area_value.set(project_data["release_valve_area"])
                print(f"已加载放散阀截面积A: {project_data['release_valve_area']}")

            # 加载小炉阀门数据
            if "small_valve_data" in project_data:
                self.small_valve_persistent_data = project_data["small_valve_data"]
                print("已加载小炉阀门数据")
            # 确保small_valve_persistent_data从正确的字段加载
            if "small_valve_data" in project_data:
                # 直接使用json中的small_valve_data字段
                self.small_valve_persistent_data = project_data["small_valve_data"]
                print(f"从项目记录中加载小炉阀门数据: {project_data['small_valve_data']}")

                # 为兼容性考虑，同时将其转换为顶层字段格式
                for i, valve_data in enumerate(self.small_valve_persistent_data):
                    if i < self.furnace_count.get():
                        if 'c_selected' in valve_data:
                            project_data[f"小炉{i+1}调节阀C选定"] = valve_data['c_selected']
                        if 'k_max' in valve_data:
                            project_data[f"小炉{i+1}调节阀K大值"] = valve_data['k_max']
                        if 'k_min' in valve_data:
                            project_data[f"小炉{i+1}调节阀K小值"] = valve_data['k_min']

            # 加载支通路支管阀前后管径
            if hasattr(self, 'bypass_vars') and self.bypass_vars:
                # 获取支通路支管阀前后管径值，如果不存在则使用默认值
                branch_pre_diameter = diameter_data.get("支通路支管阀前管径", "")
                branch_post_diameter = diameter_data.get("支通路支管阀后管径", "")
                # 打印调试信息
                print(f"原始支通路支管阀前管径值: {branch_pre_diameter}")
                print(f"原始支通路支管阀后管径值: {branch_post_diameter}")

                # 如果值为空，可以设置一个默认值
                if not branch_pre_diameter:
                    branch_pre_diameter = ""  # 设置一个合适的默认值
                if not branch_post_diameter or branch_post_diameter == "":
                    branch_post_diameter = ""  # 设置一个合适的默认值

                # 设置到界面上
                self.bypass_vars['branch_pre']['selected'].set(branch_pre_diameter)
                self.bypass_vars['branch_post']['selected'].set(branch_post_diameter)
                # 打印设置后的值
                print(f"设置后支通路支管阀前管径: {self.bypass_vars['branch_pre']['selected'].get()}")
                print(f"设置后支通路支管阀后管径: {self.bypass_vars['branch_post']['selected'].get()}")

            # 加载流速温度数据
            velocity_temp_data = project_data.get("流速温度数据", {})
            self.main_velocity.set(velocity_temp_data.get("天然气总管流速", ""))
            self.branch_velocity.set(velocity_temp_data.get("小炉支管流速", ""))
            self.temperature.set(velocity_temp_data.get("设计温度", ""))
            # 重新计算流速

            # 重新计算流速
            if hasattr(self, 'bypass_vars') and self.bypass_vars:
                # 调用自动计算方法来更新流速
                self.auto_calculate_branch_pre_velocity()
                self.auto_calculate_branch_post_velocity()
             # 【新增】加载阀门计算值
            self.main_valve_c_selected_value.set(project_data.get("main_valve_c_selected", ""))
            self.main_valve_k_max_value.set(project_data.get("main_valve_k_max", ""))
            self.main_valve_k_min_value.set(project_data.get("main_valve_k_min", ""))
            self.release_valve_dn_value.set(project_data.get("release_valve_dn", ""))

            # 【新增】加载小炉阀门数据
            small_valve_data = project_data.get("small_valve_data", [])
            if small_valve_data:
                self.small_valve_persistent_data = small_valve_data

            # 加载小炉数据
            furnace_data_list = project_data.get("小炉数据", [])
            # 确保小炉数量与数据一致
            self.update_furnace_display()

            # 加载小炉数据到界面
            for i, furnace_data in enumerate(furnace_data_list):
                if i < len(self.furnace_data):
                    self.furnace_data[i]['heat_load'].set(furnace_data.get("平均热负荷", ""))
                    self.furnace_data[i]['float_value'].set(furnace_data.get("浮动值", ""))
                    self.furnace_data[i]['nozzle_count'].set(furnace_data.get("喷枪数", ""))
                    self.furnace_data[i]['selected_diameter'].set(furnace_data.get("选取阀前管径", ""))
                    # 加载新增字段
                    self.furnace_data[i]['selected_post_diameter'].set(furnace_data.get("选取阀后管径", ""))

            # 【新增】加载阀门计算值
            if "main_valve_c_selected" in project_data:
                self.main_valve_c_selected_value.set(project_data["main_valve_c_selected"])
            if "main_valve_k_max" in project_data:
                self.main_valve_k_max_value.set(project_data["main_valve_k_max"])
            if "main_valve_k_min" in project_data:
                self.main_valve_k_min_value.set(project_data["main_valve_k_min"])
            if "release_valve_dn" in project_data:
                self.release_valve_dn_value.set(project_data["release_valve_dn"])
            if "small_valve_data" in project_data:
                self.small_valve_persistent_data = project_data["small_valve_data"]
            # 在load_project函数中确保加载放散阀相关数据

            # 【重要修改】从顶层加载放散阀数据
            if "放散阀工作压力" in project_data:
                self.release_valve_working_pressure_value.set(project_data["放散阀工作压力"])
                print(f"已加载放散阀工作压力: {project_data['放散阀工作压力']}")
            elif "release_valve_working_pressure" in project_data:  # 兼容旧格式
                self.release_valve_working_pressure_value.set(project_data["release_valve_working_pressure"])
                print(f"已加载放散阀工作压力(旧格式): {project_data['release_valve_working_pressure']}")

            if "放散阀反算d0" in project_data:
                self.release_valve_d0_value.set(project_data["放散阀反算d0"])
                print(f"已加载放散阀反算d0: {project_data['放散阀反算d0']}")
            elif "release_valve_d0" in project_data:  # 兼容旧格式
                self.release_valve_d0_value.set(project_data["release_valve_d0"])
                print(f"已加载放散阀反算d0(旧格式): {project_data['release_valve_d0']}")

            if "放散阀选取DN" in project_data:
                self.release_valve_dn_value.set(project_data["放散阀选取DN"])
                print(f"已加载放散阀选取DN: {project_data['放散阀选取DN']}")
            elif "release_valve_dn" in project_data:  # 兼容旧格式
                self.release_valve_dn_value.set(project_data["release_valve_dn"])
                print(f"已加载放散阀选取DN(旧格式): {project_data['release_valve_dn']}")
            if "放散阀截面积A" in project_data:
                self.release_valve_area_value.set(project_data["放散阀截面积A"])
                print(f"已加载放散阀截面积A: {project_data['放散阀截面积A']}")
            elif "release_valve_area" in project_data:  # 兼容旧格式
                self.release_valve_area_value.set(project_data["release_valve_area"])
                print(f"已加载放散阀截面积A(旧格式): {project_data['release_valve_area']}")

            # 在load_project函数中添加加载放散阀工作压力和反算d0的代码
            if "release_valve_working_pressure" in project_data:
                self.release_valve_working_pressure_value.set(project_data["release_valve_working_pressure"])
            if "release_valve_d0" in project_data:
                self.release_valve_d0_value.set(project_data["release_valve_d0"])
            if "release_valve_dn" in project_data:
                self.release_valve_dn_value.set(project_data["release_valve_dn"])

            # 触发计算
            self.auto_calculate_main_pipe()
            self.auto_calculate_bypass_pipe()
            self.auto_calculate_bypass_pre_velocity()  # 添加这行
            self.auto_calculate_bypass_post_velocity() # 添加这行
            self.calculate_furnace_values()
            # 确保设备表更新 - 显式触发小炉数量更新
            furnace_count = self.furnace_count.get()
            if hasattr(self, 'equipment_manager'):
                print(f"显式触发设备表更新，小炉数量: {furnace_count}")
                self.equipment_manager.update_equipment4_with_furnace_count(furnace_count)

            # 更新当前项目名称
            self.current_project = self.project_name.get() or "未命名项目"
            # 更新小炉阀门规格
            if hasattr(self, 'equipment_manager') and hasattr(self.equipment_manager, 'update_small_valve_specification'):
                print("初始化时更新所有小炉阀门规格")
                self.equipment_manager.update_small_valve_specification()  # 不传递索引，更新所有小炉阀门

            messagebox.showinfo("成功", "项目加载成功")
            print("加载项目数据:")
            for i in range(furnace_count):
                key = f"小炉{i+1}调节阀C选定"
                value = project_data.get(key, "")
                print(f"加载 {key}: {value}")
            # 加载设备表数据
            if "equipment_data" in project_data:
                self.equipment_manager.load_equipment_data(project_data["equipment_data"])
                print("已加载设备表数据")
                
                # 从设备表中提取小炉调节阀数据
                self.equipment_manager.load_small_valve_data()
                print("已从设备表提取小炉调节阀数据")
            # 根据氧枪和全氧窑选项更新按钮状态
            self.update_oxygen_tools_state()
            # 确认UI状态与加载的数据一致
            if self.has_oxygen_lance.get() == "是":
                # 确保设置相关UI组件状态
                self.oxygen_lance_button.configure(state="normal")
             # 项目加载完成后，主动更新小炉阀门信息
            if hasattr(self, 'update_small_valve_pressure_flow'):
                self.update_small_valve_pressure_flow()

            # 加载自力式阀数据
            self_operated_valve_data = {}
            for key, value in project_data.items():
                if key.startswith("自力式阀"):
                    self_operated_valve_data[key] = value

            if self_operated_valve_data and hasattr(self, 'self_operated_valve'):
                self.self_operated_valve.load_data_from_project(self_operated_valve_data)
                print("已加载自力式阀数据")

            # 项目加载完成后，主动更新自力式调压阀信息
            if hasattr(self, 'equipment_manager') and self.equipment_manager:
                self.equipment_manager.update_self_operated_pressure_valves()

        except Exception as e:
            messagebox.showerror("错误", f"加载项目时出错: {str(e)}")
    def save_project_silent(self):
        """静默保存项目，不显示消息框"""
        # 备份历史文件
        self.backup_history_file()
        # 如果历史记录文件存在，先读取当前项目的历史数据
        # 读取当前项目的历史数据
        old_data = {}
        if os.path.exists(self.history_file):
            try:
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
                    for record in history:
                        if record.get("工程名称") == self.project_name.get() and record.get("工程代号") == self.project_code.get():
                            # 保存压缩空气相关数据
                            for key in record.keys():
                                if (key.startswith("压缩空气") or
                                    key.startswith("蓄热室吹扫用气") or
                                    key.startswith("喷枪冷却用气") or
                                    key.startswith("投料机用气") or
                                    key.startswith("阀及工业电视用气") or
                                    key.startswith("冷端机组用气") or
                                    key.startswith("退火窑及红外用气") or
                                    key.startswith("支通路加热用气") or
                                    key.startswith("压延机烧边火用气") or
                                    key.startswith("压延机清理用气") or
                                    key == "连续用气总量(Nm³/h)" or
                                    key == "间歇用气总量(Nm³/h)" or
                                    key == "总用气量(Nm³/h)" or
                                    key == "计算管径(mm)" or
                                    key == "选取管径(mm)" or
                                    key == "实际流速(m/s)" or
                                    key == "进车间压力(MPa)" or
                                    key == "设计流速(m/s)" or

                                    key.startswith("氧气") or  # 新增这一行保留氧气管道数据
                                    key == "氧气小炉区数据" or  # 新增这一行保留小炉区数据
                                    # 添加天然气相关的数据键
                                    key.startswith("天然气") or
                                    key == "窑老期流量(Nm³/h)" or
                                    key == "窑老期流量" or
                                    key == "是否有0#氧枪" or  # 新增
                                    key == "是否是全氧窑" or  # 新增
                                    key == "main_valve_c_selected" or
                                    key == "main_valve_k_max" or
                                    key == "main_valve_k_min" or
                                    key == "release_valve_dn" or
                                    key == "release_valve_working_pressure" or  # 新增这一行
                                    key == "release_valve_d0" or  # 新增这一行
                                    key == "release_valve_area" or
                                    key == "放散阀工作压力" or  # 新增这一行
                                    key == "放散阀反算d0" or  # 新增这一行
                                    key == "放散阀选取DN" or  # 新增这一行
                                    key == "放散阀截面积A" or
                                    key == "主管调节阀C选定" or  # 新增中文键值
                                    key == "主管调节阀K大值" or  # 新增中文键值
                                    key == "主管调节阀K小值" or  # 新增中文键值
                                    key == "small_valve_data"):
                                    old_data[key] = record[key]
                            break
            except Exception as e:
                print(f"读取历史数据出错: {str(e)}")
            # 预处理小炉阀门顶层数据
            small_valve_toplevel = {}
            for i, valve_data in enumerate(self.small_valve_persistent_data):
                if i < self.furnace_count.get():
                    small_valve_toplevel[f"小炉{i+1}调节阀C选定"] = valve_data.get('c_selected', '')
                    small_valve_toplevel[f"小炉{i+1}调节阀K大值"] = valve_data.get('k_max', '')
                    small_valve_toplevel[f"小炉{i+1}调节阀K小值"] = valve_data.get('k_min', '')
            # 收集项目数据
            project_data = {
                "时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "工程名称": self.project_name.get(),
                "工程代号": self.project_code.get(),
                "项目类型": self.project_type.get(),
                "吨位": self.daily_capacity.get(),
                "小炉数": self.furnace_count.get(),
                "一窑几线": self.line_count.get(),
                # 修改后
                "是否有0#氧枪": hasattr(self, 'has_oxygen_lance') and self.has_oxygen_lance.get() or "",  # 新增
                "是否是全氧窑": hasattr(self, 'is_oxygen_kiln') and self.is_oxygen_kiln.get() or "",  # 新增
                # 【重要修改】添加放散阀数据到顶层
                "放散阀工作压力": self.release_valve_working_pressure_value.get(),
                "放散阀反算d0": self.release_valve_d0_value.get(),
                "放散阀选取DN": self.release_valve_dn_value.get(),
                "放散阀截面积A": self.release_valve_area_value.get(),  # 确保添加截面积A
                # 添加主管调节阀C选定到顶层(中文键)
                "主管调节阀C选定": self.main_valve_c_selected_value.get(),
                "主管调节阀K大值": self.main_valve_k_max_value.get(),
                "主管调节阀K小值": self.main_valve_k_min_value.get(),
                "流量数据": {
                "窑老期流量": self.old_flow.get(),
                "正常生产时流量": self.normal_flow.get(),
                "成型室加热流量": self.forming_flow.get(),
                "边火加热流量": self.edge_flow.get(),
                "支通路总流量": self.bypass_total_flow.get()
            },
            "压力数据": {
                "进车间压力": self.inlet_pressure.get(),
                "总管调节阀前压力": self.main_valve_pre.get(),
                "总管调节阀后压力": self.main_valve_post.get(),
                "小炉支管调节阀前压力": self.branch_valve_pre.get(),
                "小炉支管调节阀后压力": self.branch_valve_post.get(),
                "支通路总管阀前压力": self.bypass_main_valve_pre.get(),
                "支通路总管阀后压力": self.bypass_main_valve_post.get(),
                "边火阀前压力": self.bypass_vars['branch_pre']['pressure'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else "",
                "边火阀后压力": self.bypass_vars['branch_post']['pressure'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else ""

            },
            "管径数据": {
                "总管阀前管径": self.main_pre_diameter.get(),
                "总管阀后管径": self.main_post_diameter.get(),
                "支通路总管阀前管径": self.bypass_main_pre_diameter.get(),
                "支通路总管阀后管径": self.bypass_main_post_diameter.get(),
                "支通路支管阀前管径": self.bypass_vars['branch_pre']['selected'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else "",
                "支通路支管阀后管径": self.bypass_vars['branch_post']['selected'].get() if hasattr(self, 'bypass_vars') and self.bypass_vars else ""
            },
            "流速温度数据": {
                "天然气总管流速": self.main_velocity.get(),
                "小炉支管流速": self.branch_velocity.get(),
                "设计温度": self.temperature.get()
            },
            "小炉数据": [],
            # 保存阀门计算值
            "main_valve_c_selected": self.main_valve_c_selected_value.get(),
            "main_valve_k_max": self.main_valve_k_max_value.get(),
            "main_valve_k_min": self.main_valve_k_min_value.get(),
            "release_valve_dn": self.release_valve_dn_value.get(),
            "release_valve_working_pressure": self.release_valve_working_pressure_value.get(),
            "release_valve_d0": self.release_valve_d0_value.get(),
            "release_valve_area": self.release_valve_area_value.get(),
            "small_valve_data": self.small_valve_persistent_data,
             **(self.small_valve_c_values if hasattr(self, 'small_valve_c_values') else {}),

        }
             # 将小炉阀门数据添加到顶层
            for i, valve_data in enumerate(self.small_valve_persistent_data):
                if i < self.furnace_count.get():
                    project_data[f"小炉{i+1}调节阀C选定"] = valve_data.get('c_selected', '')
                    project_data[f"小炉{i+1}调节阀K大值"] = valve_data.get('k_max', '')
                    project_data[f"小炉{i+1}调节阀K小值"] = valve_data.get('k_min', '')
            # 在收集项目数据后，添加以下代码以确保压缩空气支管数据被正确保存
            if hasattr(self, 'compressed_air_calculator') and self.compressed_air_calculator:
                # 获取压缩空气支管数据
                branch_data = []
                if hasattr(self.compressed_air_calculator, 'branch_data'):
                    for idx, branch_vars in self.compressed_air_calculator.branch_data.items():
                        branch = {
                            "支管编号": idx,
                            "支管描述": branch_vars["description"].get(),
                            "流量": branch_vars["flow"].get(),
                            "压力": branch_vars["pressure"].get(),
                            "计算管径": branch_vars["calculated_diameter"].get(),
                            "选取管径": branch_vars["selected_diameter"].get(),
                            "实际流速": branch_vars["actual_velocity"].get()
                        }
                        branch_data.append(branch)

                if branch_data:
                    project_data["压缩空气支管数据"] = branch_data
                    print(f"已保存 {len(branch_data)} 个压缩空气支管数据")

                    # 同时保存为独立字段，增加兼容性
                    for branch in branch_data:
                        idx = branch.get("支管编号")
                        if idx:
                            project_data[f"压缩空气支管{idx}描述"] = branch.get("支管描述", "")
                            project_data[f"压缩空气支管{idx}用气量(Nm³/h)"] = branch.get("流量", "")
                            project_data[f"压缩空气支管{idx}压力(MPa)"] = branch.get("压力", "")
                            project_data[f"压缩空气支管{idx}计算管径(mm)"] = branch.get("计算管径", "")
                            project_data[f"压缩空气支管{idx}选取管径(mm)"] = branch.get("选取管径", "")
                            project_data[f"压缩空气支管{idx}实际流速(m/s)"] = branch.get("实际流速", "")
            # 在save_project函数中
            if hasattr(self, 'saving_main_valve_data') and self.saving_main_valve_data:
                # 如果正在保存主管调节阀数据，强制使用当前值
                project_data["主管调节阀C选定"] = self.main_valve_c_selected_value.get()
                project_data["主管调节阀K最大值"] = self.main_valve_k_max_value.get()
                if hasattr(self, 'main_valve_k_min_value'):
                    project_data["主管调节阀K最小值"] = self.main_valve_k_min_value.get()

            # 收集小炉数据
            for furnace in self.furnace_data:
                furnace_data = {
                "平均热负荷": furnace['heat_load'].get(),
                "浮动值": furnace['float_value'].get(),
                "喷枪数": furnace['nozzle_count'].get(),
                "选取阀前管径": furnace['selected_diameter'].get(),
                "选取阀后管径": furnace['selected_post_diameter'].get(),
                "计算阀后管径": furnace['calc_post_diameter'].cget("text"),
                "反算阀后流速": furnace['calc_post_velocity'].cget("text")
            }
                project_data["小炉数据"].append(furnace_data)


            # 读取现有历史记录
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            else:
                history = []
             # 查找是否存在相同记录
            found = False
            for i, record in enumerate(history):
                if record.get("工程名称") == project_data["工程名称"] and record.get("工程代号") == project_data["工程代号"]:
                    # 保留重要数据
                    important_data = {}
                    for key in record:
                        # 保存所有希望保留的数据
                        if (key.startswith("氧气") or
                            key.startswith("天然气") or
                            key.startswith("压缩空气") or
                            key.startswith("自力式阀") or
                            key.startswith("release_valve_") or
                            key.startswith("main_valve_") or
                            key.startswith("放散阀") or
                            key.startswith("主管调节阀") or
                            key == "氧气小炉区数据" or
                            key == "窑老期流量(Nm³/h)" or
                            key == "窑老期流量" or
                            key == "是否有0#氧枪" or
                            key == "是否是全氧窑" or
                            key == "small_valve_data" or
                            (key == "计算类型" and record[key] == "全氧窑氧气管道计算")):
                            important_data[key] = record[key]
                        if "氧枪氧气数据" in record:
                            important_data["氧枪氧气数据"] = record["氧枪氧气数据"]
                        if "氧枪天然气数据" in record:
                            important_data["氧枪天然气数据"] = record["氧枪天然气数据"]
                        if "是否有0#氧枪" in record:
                            important_data["是否有0#氧枪"] = record["是否有0#氧枪"]

                    # 更新记录，但保留重要数据
                    temp_data = project_data.copy()
                    # 另外还要检查以下类型的数据保存
                    for key in record:
                        if key.startswith("氧枪_"):
                            important_data[key] = record[key]
                    # 将原记录中的重要数据合并到新数据中
                    for key, value in important_data.items():
                        # 如果新数据中不存在该键，或者新数据对应值为空，则使用旧数据
                        if key not in temp_data or not temp_data[key]:
                            temp_data[key] = value

                    history[i] = temp_data  # 更新记录
                    found = True
                    break

            if not found:
                history.append(project_data)  # 添加新记录

             # 添加现有的数据
            if old_data:
                for key, value in old_data.items():
                    if key not in project_data:
                        project_data[key] = value

        # 【添加以下代码 - 将项目数据写入历史文件】
        try:
            # 使用found变量来控制，避免重复删除和添加
            if not found:  # 如果前面的代码没有找到并更新记录
                # 删除同名项目
                history = [record for record in history if not (record.get("工程名称") == project_data["工程名称"] and record.get("工程代号") == project_data["工程代号"])]
                # 添加新的记录
                history.append(project_data)

            # 写入历史记录文件
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)

            print(f"项目 '{project_data['工程名称']}' 已静默保存")
            print(f"保存的调节阀数据: C选定={project_data.get('主管调节阀C选定')}, 放散阀DN={project_data.get('放散阀选取DN')}")
            print("保存项目数据:")
            for i in range(self.furnace_count.get()):
                key = f"小炉{i+1}调节阀C选定"
                value = project_data.get(key, "")
                print(f"保存 {key}: {value}")

            return True

        except Exception as e:
            print(f"保存项目时出错: {str(e)}")
            return False
    # 在show_history_manager方法中添加编辑按钮和相关功能
    def edit_selected_record():
        """编辑选中的历史记录"""
        try:
            selected = history_tree.selection()
            if not selected:
                messagebox.showinfo("提示", "请先选择一条记录")
                return

            # 获取选中记录的数据
            values = history_tree.item(selected[0], "values")
            if not values or len(values) < 2:
                return

            # 从历史数据中查找匹配记录
            for i, record in enumerate(history):
                if (record.get("时间") == values[0] and
                    record.get("工程名称") == values[1] and
                    record.get("工程代号") == values[2]):

                    # 创建编辑窗口
                    edit_window = tk.Toplevel(history_window)
                    edit_window.title("编辑历史记录")
                    edit_window.geometry("400x300")
                    edit_window.resizable(False, False)

                    # 创建编辑表单
                    ttk.Label(edit_window, text="工程名称:").grid(row=0, column=0, padx=5, pady=5, sticky="w")
                    project_name_var = tk.StringVar(value=record.get("工程名称", ""))
                    ttk.Entry(edit_window, textvariable=project_name_var, width=30).grid(row=0, column=1, padx=5, pady=5, sticky="w")

                    ttk.Label(edit_window, text="工程代号:").grid(row=1, column=0, padx=5, pady=5, sticky="w")
                    project_code_var = tk.StringVar(value=record.get("工程代号", ""))
                    ttk.Entry(edit_window, textvariable=project_code_var, width=30).grid(row=1, column=1, padx=5, pady=5, sticky="w")

                    ttk.Label(edit_window, text="项目类型:").grid(row=2, column=0, padx=5, pady=5, sticky="w")
                    project_type_var = tk.StringVar(value=record.get("项目类型", ""))
                    ttk.Entry(edit_window, textvariable=project_type_var, width=30).grid(row=2, column=1, padx=5, pady=5, sticky="w")

                    # 保存按钮
                    def save_edit():
                        # 更新记录
                        record["工程名称"] = project_name_var.get()
                        record["工程代号"] = project_code_var.get()
                        record["项目类型"] = project_type_var.get()

                        # 保存更新后的历史记录
                        try:
                            with open(self.history_file, 'w', encoding='utf-8') as f:
                                json.dump(history, f, ensure_ascii=False, indent=2)

                            # 更新显示
                            search_records()
                            messagebox.showinfo("成功", "记录已更新")
                            edit_window.destroy()
                        except Exception as e:
                            messagebox.showerror("错误", f"保存更新时出错: {str(e)}")

                    ttk.Button(edit_window, text="保存", command=save_edit).grid(row=3, column=0, columnspan=2, padx=5, pady=10)

                    # 设置窗口居中
                    self.center_window(edit_window)
                    return

            messagebox.showinfo("提示", "未找到匹配的历史记录")
        except Exception as e:
            messagebox.showerror("错误", f"编辑记录时出错: {str(e)}")
    def update_equipment_diameter(self, *args):
        """代理方法，将调用转发到equipment_manager"""
        self.equipment_manager.update_equipment_diameter(*args)
    # 添加新方法来处理调节阀前后压力变化
    def update_equipment_valve_pressure(self, *args):
        """当调节阀前后压力变化时，确保设备表更新"""
        print("调节阀前后压力变化, 正在更新设备表...")
        
        # 确保equipment_manager已初始化
        if hasattr(self, 'equipment_manager'):
            # 直接调用设备表的更新方法
            if hasattr(self.equipment_manager, 'update_valve_pressure'):
                try:
                    self.equipment_manager.update_valve_pressure()
                    print("设备表压力信息更新完成")
                except Exception as e:
                    print(f"更新设备表压力信息时出错: {str(e)}")
    # 在GasCalculator类中添加此方法
    def update_small_valve_pressure_flow(self, *args):
        """当小炉支管调节阀前后压力变化时，确保设备表更新"""
        print("小炉支管调节阀前后压力变化, 正在更新设备表...")
        
        # 确保equipment_manager已初始化
        if hasattr(self, 'equipment_manager'):
            # 直接调用设备表的更新方法
            if hasattr(self.equipment_manager, 'update_small_valve_pressure_flow'):
                try:
                    self.equipment_manager.update_small_valve_pressure_flow()
                    print("设备表小炉阀门信息更新完成")
                except Exception as e:
                    print(f"更新设备表小炉阀门信息时出错: {str(e)}")
        

if __name__ == "__main__":
    root = tk.Tk()
    app = GasCalculator(root)
    root.mainloop()







